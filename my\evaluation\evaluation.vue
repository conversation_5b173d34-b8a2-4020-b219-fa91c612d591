<template>
	<view style="padding-bottom: 20rpx;">
		<view class="list flex justify-center align-center" v-if="list.length>0">
			<view class="list-box">
				<view class="list-item" v-for="(item,index) in list" :key="index">
					<view class="list-item-header flex align-center justify-between">
						<view class="list-item-header-l flex align-center">
							<image :src="item.rideAvatar" style="width: 100rpx;height: 100rpx;border-radius: 50%;" mode=""></image>
							<text style="margin: 0 20rpx;">{{item.rideName}}</text>
						</view>
						<view class="list-item-header-r">
							<u-rate disabled :count="count" v-model="item.satisfactionFlag"></u-rate>
						</view>
						
						
					</view>
					<view class="list-item-content">
						{{item.evaluateMessage}}
					</view>
					<view class="list-item-image flex justify-between flex-wrap">
						<image @click="priveImgs(ind,item.image)" :src="ite" v-for="(ite,ind) in item.evaluateImg?item.evaluateImg.split(','):[]" :key="ind"
							mode=""></image>
						<view class="" style="width: 200rpx;height: 0;">

						</view>
					</view>
					<view class="list-item-cz flex justify-between align-center">
						<view class="list-item-time">
							{{item.createTime}}
						</view>
						<view @click="deleteImg(item)" class="list-item-cz-b">
							删除
						</view>
					</view>
				</view>
			</view>

		</view>

		<empty v-if="list.length==0" />
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				count: 5,
				list: [],
				page:1,
				limit:10,
				pages:1,
			};
		},
		//下拉刷新
		onPullDownRefresh() {
			this.page = 1
			this.getList()
		},
		//上拉加载更多
		onReachBottom() {
			if(this.page < this.pages){
				this.page += 1
				this.getList()
			}
		},
		onLoad() {
			uni.showLoading({
				title:'加载中...'
			})
			this.getList()
		},
		methods: {
			//评价列表
			getList(){
				let data = {
					page:1,
					limit:10,
					userId:uni.getStorageSync('userId')
				}
				this.$Request.getT('/app/evaluate/getUserEvaluate',data).then(res=>{
					uni.hideLoading()
					uni.stopPullDownRefresh()
					if(res.code == 0){
						this.pages = res.data.pages
						if(this.page==1){
							this.list = res.data.records
						} else{
							this.list = [...this.list,...res.data.records]
						}
					}
				})
			},
			//删除图片
			deleteImg(item){
				let that = this
				uni.showModal({
					title:'提示',
					content:'确定删除该评价吗？',
					complete(ret) {
						if(ret.confirm){
							let data = {
								evaluateId:item.evaluateId
							}
							that.$Request.getT('/app/evaluate/deleteEvaluate',data).then(res=>{
								if(res.code == 0){
									uni.showToast({
										title:'已删除'
									})
									that.getList()
								}else{
									uni.showToast({
										title:res.msg,
										icon:'none'
									})
								}
							})
						}
					}
				})
			},
			//预览图片
			priveImgs(index, imgs) {
				uni.previewImage({
					current: index,
					urls: imgs
				})
			},
		},
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.list {
		width: 100%;
		height: auto;

		.list-box {
			width: 686rpx;
			height: 100%;
		}

		.list-item {
			width: 100%;
			border-radius: 10px;
			background-color: #ffffff;
			margin-top: 20rpx;
			padding: 0 20rpx;
			.list-item-header{
				width: 100%;
				padding-top: 20rpx;
			}
			.list-item-content {
				width: 100%;
				padding-top: 20rpx;
			}

			.list-item-image {
				width: 100%;
				margin-top: 20rpx;

				image {
					width: 200rpx;
					height: 200rpx;
					margin-bottom: 20rpx;
					border-radius: 8rpx;
				}
			}

			.list-item-time{
				// padding-bottom: 20rpx;
				color: #999999;
			}
			.list-item-cz {
				width: 100%;
				padding-bottom: 20rpx;
				.list-item-cz-b {
					color: #0175FE;
					background: #D9EBFF;
					padding: 7px 15px;
					border-radius: 25px;
					margin-left: 10px;
					font-size: 12px;
				}
			}
		}
	}
</style>