<template>
	<view>
		<phone-directory :phones="phones" @paramClick="paramClick"></phone-directory>
	</view>
</template>

<script>
	import phoneDirectory from '@/components/phone-directory/phone-directory.vue'
	export default {
		name:"phones",
		components:{
			phoneDirectory
		},
		data() {
			return {
				//示例数据
				phones:{
					"A": [{
						"id": 56,
						"spell": "aba",
						"name": "阿坝"
					}, {
						"id": 57,
						"spell": "akesu",
						"name": "阿克苏"
					}, {
						"id": 58,
						"spell": "alashanmeng",
						"name": "阿拉善盟"
					}, {
						"id": 59,
						"spell": "aletai",
						"name": "阿勒泰"
					}, {
						"id": 60,
						"spell": "ali",
						"name": "阿里"
					}, {
						"id": 61,
						"spell": "ankang",
						"name": "安康"
					}, {
						"id": 62,
						"spell": "anqing",
						"name": "安庆"
					}, {
						"id": 63,
						"spell": "anshan",
						"name": "鞍山"
					}, {
						"id": 64,
						"spell": "anshun",
						"name": "安顺"
					}, {
						"id": 65,
						"spell": "anyang",
						"name": "安阳"
					}, {
						"id": 338,
						"spell": "acheng",
						"name": "阿城"
					}, {
						"id": 339,
						"spell": "anfu",
						"name": "安福"
					}, {
						"id": 340,
						"spell": "anji",
						"name": "安吉"
					}, {
						"id": 341,
						"spell": "anning",
						"name": "安宁"
					}, {
						"id": 342,
						"spell": "anqiu",
						"name": "安丘"
					}, {
						"id": 343,
						"spell": "anxi",
						"name": "安溪"
					}, {
						"id": 344,
						"spell": "anyi",
						"name": "安义"
					}, {
						"id": 345,
						"spell": "anyuan",
						"name": "安远"
					}],
					"B": [{
						"id": 1,
						"spell": "beijing",
						"name": "北京"
					}, {
						"id": 66,
						"spell": "baicheng",
						"name": "白城"
					}, {
						"id": 67,
						"spell": "baise",
						"name": "百色"
					}, {
						"id": 68,
						"spell": "baishan",
						"name": "白山"
					}, {
						"id": 69,
						"spell": "baiyin",
						"name": "白银"
					}, {
						"id": 70,
						"spell": "bangbu",
						"name": "蚌埠"
					}, {
						"id": 71,
						"spell": "baoding",
						"name": "保定"
					}, {
						"id": 72,
						"spell": "baoji",
						"name": "宝鸡"
					}, {
						"id": 73,
						"spell": "baoshan",
						"name": "保山"
					}, {
						"id": 74,
						"spell": "baotou",
						"name": "包头"
					}, {
						"id": 75,
						"spell": "bayannaoer",
						"name": "巴彦淖尔"
					}, {
						"id": 76,
						"spell": "bayinguoleng",
						"name": "巴音郭楞"
					}, {
						"id": 77,
						"spell": "bazhong",
						"name": "巴中"
					}, {
						"id": 78,
						"spell": "beihai",
						"name": "北海"
					}, {
						"id": 79,
						"spell": "benxi",
						"name": "本溪"
					}, {
						"id": 80,
						"spell": "bijie",
						"name": "毕节"
					}, {
						"id": 81,
						"spell": "binzhou",
						"name": "滨州"
					}, {
						"id": 82,
						"spell": "boertala",
						"name": "博尔塔拉"
					}, {
						"id": 83,
						"spell": "bozhou",
						"name": "亳州"
					}, {
						"id": 346,
						"spell": "baoying",
						"name": "宝应"
					}, {
						"id": 347,
						"spell": "bayan",
						"name": "巴彦"
					}, {
						"id": 348,
						"spell": "binhai",
						"name": "滨海"
					}, {
						"id": 349,
						"spell": "binxian",
						"name": "宾县"
					}, {
						"id": 350,
						"spell": "binyang",
						"name": "宾阳"
					}, {
						"id": 351,
						"spell": "bishan",
						"name": "璧山"
					}, {
						"id": 352,
						"spell": "boai",
						"name": "博爱"
					}, {
						"id": 353,
						"spell": "boluo",
						"name": "博罗"
					}, {
						"id": 354,
						"spell": "boxing",
						"name": "博兴"
					}],
					"C": [{
						"id": 2,
						"spell": "chongqing",
						"name": "重庆"
					}, {
						"id": 5,
						"spell": "changchun",
						"name": "长春"
					}, {
						"id": 6,
						"spell": "changsha",
						"name": "长沙"
					}, {
						"id": 7,
						"spell": "changzhou",
						"name": "常州"
					}, {
						"id": 8,
						"spell": "chengdu",
						"name": "成都"
					}, {
						"id": 84,
						"spell": "cangzhou",
						"name": "沧州"
					}, {
						"id": 85,
						"spell": "changde",
						"name": "常德"
					}, {
						"id": 86,
						"spell": "changdu",
						"name": "昌都"
					}, {
						"id": 87,
						"spell": "changji",
						"name": "昌吉"
					}, {
						"id": 88,
						"spell": "changzhi",
						"name": "长治"
					}, {
						"id": 89,
						"spell": "chaohu",
						"name": "巢湖"
					}, {
						"id": 90,
						"spell": "chaoyang",
						"name": "朝阳"
					}, {
						"id": 91,
						"spell": "chaozhou",
						"name": "潮州"
					}, {
						"id": 92,
						"spell": "chengde",
						"name": "承德"
					}, {
						"id": 93,
						"spell": "chenzhou",
						"name": "郴州"
					}, {
						"id": 94,
						"spell": "chifeng",
						"name": "赤峰"
					}, {
						"id": 95,
						"spell": "chizhou",
						"name": "池州"
					}, {
						"id": 96,
						"spell": "chongzuo",
						"name": "崇左"
					}, {
						"id": 97,
						"spell": "chuxiong",
						"name": "楚雄"
					}, {
						"id": 98,
						"spell": "chuzhou",
						"name": "滁州"
					}, {
						"id": 355,
						"spell": "cangnan",
						"name": "苍南"
					}, {
						"id": 356,
						"spell": "cangshan",
						"name": "苍山"
					}, {
						"id": 357,
						"spell": "caoxian",
						"name": "曹县"
					}, {
						"id": 358,
						"spell": "changdao",
						"name": "长岛"
					}, {
						"id": 359,
						"spell": "changfeng",
						"name": "长丰"
					}, {
						"id": 360,
						"spell": "changhai",
						"name": "长海"
					}, {
						"id": 361,
						"spell": "changle",
						"name": "长乐"
					}, {
						"id": 362,
						"spell": "changle",
						"name": "昌乐"
					}, {
						"id": 363,
						"spell": "changshan",
						"name": "常山"
					}, {
						"id": 364,
						"spell": "changshu",
						"name": "常熟"
					}, {
						"id": 365,
						"spell": "changtai",
						"name": "长泰"
					}, {
						"id": 366,
						"spell": "changting",
						"name": "长汀"
					}, {
						"id": 367,
						"spell": "changxing",
						"name": "长兴"
					}, {
						"id": 368,
						"spell": "changyi",
						"name": "昌邑"
					}, {
						"id": 369,
						"spell": "chaoan",
						"name": "潮安"
					}, {
						"id": 370,
						"spell": "chenggong",
						"name": "呈贡"
					}, {
						"id": 371,
						"spell": "chengkou",
						"name": "城口"
					}, {
						"id": 372,
						"spell": "chengwu",
						"name": "成武"
					}, {
						"id": 373,
						"spell": "chiping",
						"name": "茌平"
					}, {
						"id": 374,
						"spell": "chongren",
						"name": "崇仁"
					}, {
						"id": 375,
						"spell": "chongyi",
						"name": "崇义"
					}, {
						"id": 376,
						"spell": "chongzhou",
						"name": "崇州"
					}, {
						"id": 377,
						"spell": "chunan",
						"name": "淳安"
					}, {
						"id": 378,
						"spell": "cixi",
						"name": "慈溪"
					}, {
						"id": 379,
						"spell": "conghua",
						"name": "从化"
					}, {
						"id": 380,
						"spell": "congyang",
						"name": "枞阳"
					}],
					"K": [{
						"id": 25,
						"spell": "kunming",
						"name": "昆明"
					}, {
						"id": 174,
						"spell": "kaifeng",
						"name": "开封"
					}, {
						"id": 175,
						"spell": "kashidi",
						"name": "喀什地"
					}, {
						"id": 176,
						"spell": "kelamayi",
						"name": "克拉玛依"
					}, {
						"id": 177,
						"spell": "kezile",
						"name": "克孜勒"
					}, {
						"id": 555,
						"spell": "kaihua",
						"name": "开化"
					}, {
						"id": 556,
						"spell": "kaiping",
						"name": "开平"
					}, {
						"id": 557,
						"spell": "kaixian",
						"name": "开县"
					}, {
						"id": 558,
						"spell": "kaiyang",
						"name": "开阳"
					}, {
						"id": 559,
						"spell": "kangping",
						"name": "康平"
					}, {
						"id": 560,
						"spell": "kenli",
						"name": "垦利"
					}, {
						"id": 561,
						"spell": "kunshan",
						"name": "昆山"
					}],
					"M": [{
						"id": 203,
						"spell": "maanshan",
						"name": "马鞍山"
					}, {
						"id": 204,
						"spell": "maoming",
						"name": "茂名"
					}],
					"S": [{
						"id": 3,
						"spell": "shanghai",
						"name": "上海"
					}, {
						"id": 36,
						"spell": "shenyang",
						"name": "沈阳"
					}, {
						"id": 37,
						"spell": "shenzhen",
						"name": "深圳"
					}, {
						"id": 38,
						"spell": "shijiazhuang",
						"name": "石家庄"
					}, {
						"id": 39,
						"spell": "suzhou",
						"name": "苏州"
					}, {
						"id": 237,
						"spell": "sanmenxia",
						"name": "三门峡"
					}, {
						"id": 238,
						"spell": "sanming",
						"name": "三明"
					}, {
						"id": 239,
						"spell": "sanya",
						"name": "三亚"
					}, {
						"id": 240,
						"spell": "shangluo",
						"name": "商洛"
					}, {
						"id": 241,
						"spell": "shangqiu",
						"name": "商丘"
					}, {
						"id": 242,
						"spell": "shangrao",
						"name": "上饶"
					}, {
						"id": 243,
						"spell": "shannan",
						"name": "山南"
					}, {
						"id": 244,
						"spell": "shantou",
						"name": "汕头"
					}, {
						"id": 245,
						"spell": "shanwei",
						"name": "汕尾"
					}, {
						"id": 246,
						"spell": "shaoguan",
						"name": "韶关"
					}, {
						"id": 247,
						"spell": "shaoxing",
						"name": "绍兴"
					}, {
						"id": 248,
						"spell": "shaoyang",
						"name": "邵阳"
					}, {
						"id": 249,
						"spell": "shiyan",
						"name": "十堰"
					}, {
						"id": 250,
						"spell": "shizuishan",
						"name": "石嘴山"
					}, {
						"id": 251,
						"spell": "shuangyashan",
						"name": "双鸭山"
					}, {
						"id": 252,
						"spell": "shuozhou",
						"name": "朔州"
					}, {
						"id": 253,
						"spell": "siping",
						"name": "四平"
					}, {
						"id": 254,
						"spell": "songyuan",
						"name": "松原"
					}, {
						"id": 255,
						"spell": "suihua",
						"name": "绥化"
					}, {
						"id": 256,
						"spell": "suining",
						"name": "遂宁"
					}],
					"T": [{
						"id": 4,
						"spell": "tianjin",
						"name": "天津"
					}, {
						"id": 40,
						"spell": "taizhou",
						"name": "台州"
					}, {
						"id": 41,
						"spell": "tangshan",
						"name": "唐山"
					}, {
						"id": 260,
						"spell": "tachengdi",
						"name": "塔城地"
					}, {
						"id": 261,
						"spell": "taian",
						"name": "泰安"
					}, {
						"id": 262,
						"spell": "taiyuan",
						"name": "太原"
					}, {
						"id": 263,
						"spell": "taizhou",
						"name": "泰州"
					}, {
						"id": 264,
						"spell": "tianshui",
						"name": "天水"
					}, {
						"id": 265,
						"spell": "tieling",
						"name": "铁岭"
					}, {
						"id": 266,
						"spell": "tongchuan",
						"name": "铜川"
					}, {
						"id": 267,
						"spell": "tonghua",
						"name": "通化"
					}, {
						"id": 268,
						"spell": "tongliao",
						"name": "通辽"
					}],
					"X": [{
						"id": 46,
						"spell": "xiamen",
						"name": "厦门"
					}, {
						"id": 47,
						"spell": "xian",
						"name": "西安"
					}, {
						"id": 48,
						"spell": "xuchang",
						"name": "许昌"
					}],
					"Y": [{
						"id": 50,
						"spell": "yangzhou",
						"name": "扬州"
					}, {
						"id": 51,
						"spell": "yantai",
						"name": "烟台"
					}, {
						"id": 298,
						"spell": "yaan",
						"name": "雅安"
					}, {
						"id": 299,
						"spell": "yanan",
						"name": "延安"
					}],
					"Z": [{
						"id": 52,
						"spell": "zhangzhou",
						"name": "漳州"
					}, {
						"id": 53,
						"spell": "zhengzhou",
						"name": "郑州"
					}, {
						"id": 54,
						"spell": "zhongshan",
						"name": "中山"
					}, {
						"id": 55,
						"spell": "zhuhai",
						"name": "珠海"
					}, {
						"id": 321,
						"spell": "zaozhuang",
						"name": "枣庄"
					}, {
						"id": 322,
						"spell": "zhengzhou",
						"name": "郑州"
					}, {
						"id": 323,
						"spell": "zhongshan",
						"name": "中山"
					}, {
						"id": 324,
						"spell": "zhuhai",
						"name": "珠海"
					}, {
						"id": 325,
						"spell": "zaozhuang",
						"name": "枣庄"
					}, {
						"id": 326,
						"spell": "zhengzhou",
						"name": "郑州"
					}, {
						"id": 254,
						"spell": "zhongshan",
						"name": "中山"
					}, {
						"id": 355,
						"spell": "zhuhai",
						"name": "珠海"
					}, {
						"id": 121,
						"spell": "zaozhuang",
						"name": "枣庄"
					}, {
						"id": 453,
						"spell": "zhengzhou",
						"name": "郑州"
					}, {
						"id": 554,
						"spell": "zhongshan",
						"name": "中山"
					}, {
						"id": 255,
						"spell": "zhuhai",
						"name": "珠海"
					}, {
						"id": 368,
						"spell": "zaozhuang",
						"name": "枣庄"
					}, {
						"id": 369,
						"spell": "zhengzhou",
						"name": "郑州"
					}, {
						"id": 754,
						"spell": "zhongshan",
						"name": "中山"
					}, {
						"id": 655,
						"spell": "zhuhai",
						"name": "珠海"
					}, {
						"id": 668,
						"spell": "zaozhuang",
						"name": "枣庄"
					}]
					
				}
			
			}
		},
		methods : {
			paramClick (e) {
				console.log(e)
			}
		}
	}
</script>

<style>

</style>
