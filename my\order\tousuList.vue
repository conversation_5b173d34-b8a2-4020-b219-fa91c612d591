<template>
	<view>
		<view class="bonus-main">
			<view class="list-item " v-if="dataList.length>0" :show-arrow="false" v-for="(item, index) in dataList"
				:key="index">
				<view class="list-item-wrap margin-top-sm">
					<view v-if="item.image && item.image.length>0" class="padding-tb-sm">
						<swiper class="screen-swiper" style="height: 260rpx;" :circular="true" :autoplay="true"
							duration="800">
							<swiper-item v-for="(item,index) in item.image" :key="index"
								@click="saveImg(item.image,index)">
								<image :src="item" mode="aspectFit" class="radius"></image>
							</swiper-item>
						</swiper>
					</view>
					<view style="padding: 20rpx 30rpx;">
						<view>投诉标题：{{item.title}}</view>

						<view class="list-title" style="color: #333;margin-top: 10upx;" v-if="item.content">投诉内容：{{ item.content }}</view>
						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.byUserName">
							被投诉人：{{ item.byUserName }}</view>
						<view class="margin-top-xs flex align-center" v-if="item.platform==3">订单投诉：{{item.url}}	<image src="../static/copy.png" style="width: 45rpx;height: 45rpx;margin-left: 5upx;"
								@click.stop="copyClick(item.url)"></image></view>
						<view style="display: flex;justify-content: space-between;margin-top: 10rpx;">
							<view>
								<text style="color: #333;" v-if="item.createAt">投诉时间：{{ item.createAt }}</text>
							</view>
						</view>

						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.status==0">状态：待处理
						</view>
						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.status==4">状态：不处理
						</view>
						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.status==1">状态：已驳回
						</view>
						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.status==2">状态：已封号

						</view>
						<view class="list-title" style="margin-top: 10upx;color: #333;" v-if="item.status==3">状态：已处理

						</view>
						<view class="list-title" style="margin-top: 10upx;color: #333" v-if="item.status==1">
							驳回内容：{{ item.auditContent }}</view>
						<view class="list-title" style="margin-top: 10upx;color: #333" v-if="item.status==3">
							处理内容：{{ item.auditContent }}</view>
					</view>
				</view>
			</view>

			<empty v-if="dataList.length === 0" des="暂无记录" show="false"></empty>
		</view>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				page: 1,
				limit: 10,
				count: 0,
				dataList: []
			}
		},
		onLoad() {
			this.tousuList()
		},
		onShow() {

		},
		methods: {
			copyClick(copy) {
				uni.setClipboardData({
					data: copy,
					success: function(res) {
						uni.getClipboardData({
							success: function(res) {
								uni.showToast({
									title: "复制成功",
									icon: 'none',
								});
							},
						});
					},
				});
			},
			// 查看图片
			saveImg(imgs, index) {
				if (this.LBSelect) {
					if (index == this.LBIndex - 1) {
						return;
					}
				}
				// console.log(imgs)
				let that = this;
				let imgArr = imgs
				// imgArr.push(imgs);
				// //预览图片
				uni.previewImage({
					urls: imgArr,
					current: imgArr[index]
				});
			},
			// get page limit state 3 
			tousuList() {
				let data = {
					page: this.page,
					limit: this.limit,
					state: 3
				}
				this.$Request.get('/app/message/selectMessageByUserId', data).then(res => {
					if (res.code == 0) {

						if (this.page == 1) {
							this.dataList = res.data.list
							this.dataList.map(item=>{
								if(item.image){
									item.image = item.image.split(',')
								}
							})
						} else {
							this.dataList = [...this.dataList, ...res.data.list]
							this.dataList.map(item=>{
								if(item.image){
									item.image = item.image.split(',')
								}
							})
						}
						this.count = res.data.totalCount
					}

				})
			}
		},
		onReachBottom: function() {
			if (this.count == this.dataList.length) {
				uni.showToast({
					title: '已经到底了',
					icon: 'none'
				})
				return
			} else {
				this.page = this.page + 1;
				this.tousuList()
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.tousuList()
		},
	}
</script>

<style>
	page {
		background-color: #F7F7F7;
	}

	.bg {
		background: #FFFFFF;
	}

	.list-item {
		background: #FFFFFF;
		border-radius: 16rpx;
		margin: 0 20rpx 20rpx 20rpx;
	}

	.img1 {
		width: 100%;
		height: 400rpx;
	}

	.img2 {
		width: 210rpx;
		height: 210rpx;
		margin-top: 15rpx;
		margin-right: 5rpx;
		margin-left: 15rpx;
	}
</style>
