@charset "UTF-8";
@import "./icon.css";

/* Simple Pro 简
 * Author 芥末
 * 2018-09-27
 */
/* 全局变量  */
.s-page-wrapper {
    max-width: 750 rpx;
}

/* 含有阴影 */
.has-shadow {
    box-shadow: 3px 5px 7px 3px rgba(29, 29, 31, 0.09);
}

/* 含有边框 */
.has-border {
    border: 1px solid #dcdee2;
}

.has-radius {
    border-radius: 4px;
}

.has-break {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 混合颜色 */
/* 网格 */
.is-flex {
    display: flex;
}

.is-block {
    display: block;
}

.is-column {
    flex-direction: column;
}

.s-row {
    position: relative;
    margin-left: 0;
    margin-right: 0;
    height: auto;
    zoom: 1;
    display: block;
}

.s-row::after,
.s-row::before {
    content: "";
    display: table;
}

.s-row::after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0;
}

.s-row-flex {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.s-row-flex,
.s-row-flex::after,
.s-row-flex::before {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.s-col {
    flex-direction: column;
}

.is-justify-end {
    justify-content: flex-end;
}

.is-justify-center {
    justify-content: center;
}

.is-justify-start {
    justify-content: flex-start;
}

.is-justify-between {
    justify-content: space-between;
}

.is-justify-around {
    justify-content: space-around;
}

.is-align-start {
    align-items: flex-start;
}

.is-align-center {
    align-items: center;
}

.is-align-end {
    align-items: flex-end;
}

.is-align-stretch {
    align-items: stretch;
}

.s-col {
    position: relative;
    display: block;
    box-sizing: border-box;
}

.is-col-1,
.is-col-2,
.is-col-3,
.is-col-4,
.is-col-5,
.is-col-6,
.is-col-7,
.is-col-8,
.is-col-9,
.is-col-10,
.is-col-11,
.is-col-12,
.is-col-13,
.is-col-14,
.is-col-15,
.is-col-16,
.is-col-17,
.is-col-18,
.is-col-19,
.is-col-20,
.is-col-21,
.is-col-22,
.is-col-23,
.is-col-24,
.is-col-1-5,
.is-col-1-8 {
    float: left;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
}

.is-col-1-5 {
    display: block;
    width: 20%;
}

.is-push-1-5 {
    left: 20%;
}

.is-pull-1-5 {
    right: 20%;
}

.is-offset-1-5 {
    margin-left: 20%;
}

.is-col-1-8 {
    display: block;
    width: 12.5%;
}

.is-push-1-8 {
    left: 12.5%;
}

.is-pull-1-8 {
    right: 12.5%;
}

.is-offset-1-8 {
    margin-left: 12.5%;
}

.is-col-24 {
    display: block;
    width: 100%;
}

.is-push-24 {
    left: 100%;
}

.is-pull-24 {
    right: 100%;
}

.is-offset-24 {
    margin-left: 100%;
}

.is-order-24 {
    -webkit-box-ordinal-group: 25;
    -ms-flex-order: 24;
    order: 24;
}

.is-col-23 {
    display: block;
    width: 95.83333333%;
}

.is-push-23 {
    left: 95.83333333%;
}

.is-pull-23 {
    right: 95.83333333%;
}

.is-offset-23 {
    margin-left: 95.83333333%;
}

.is-order-23 {
    -webkit-box-ordinal-group: 24;
    -ms-flex-order: 23;
    order: 23;
}

.is-col-22 {
    display: block;
    width: 91.66666667%;
}

.is-push-22 {
    left: 91.66666667%;
}

.is-pull-22 {
    right: 91.66666667%;
}

.is-offset-22 {
    margin-left: 91.66666667%;
}

.is-order-22 {
    -webkit-box-ordinal-group: 23;
    -ms-flex-order: 22;
    order: 22;
}

.is-col-21 {
    display: block;
    width: 87.5%;
}

.is-push-21 {
    left: 87.5%;
}

.is-pull-21 {
    right: 87.5%;
}

.is-offset-21 {
    margin-left: 87.5%;
}

.is-order-21 {
    -webkit-box-ordinal-group: 22;
    -ms-flex-order: 21;
    order: 21;
}

.is-col-20 {
    display: block;
    width: 83.33333333%;
}

.is-push-20 {
    left: 83.33333333%;
}

.is-pull-20 {
    right: 83.33333333%;
}

.is-offset-20 {
    margin-left: 83.33333333%;
}

.is-order-20 {
    -webkit-box-ordinal-group: 21;
    -ms-flex-order: 20;
    order: 20;
}

.is-col-19 {
    display: block;
    width: 79.16666667%;
}

.is-push-19 {
    left: 79.16666667%;
}

.is-pull-19 {
    right: 79.16666667%;
}

.is-offset-19 {
    margin-left: 79.16666667%;
}

.is-order-19 {
    -webkit-box-ordinal-group: 20;
    -ms-flex-order: 19;
    order: 19;
}

.is-col-18 {
    display: block;
    width: 75%;
}

.is-push-18 {
    left: 75%;
}

.is-pull-18 {
    right: 75%;
}

.is-offset-18 {
    margin-left: 75%;
}

.is-order-18 {
    -webkit-box-ordinal-group: 19;
    -ms-flex-order: 18;
    order: 18;
}

.is-col-17 {
    display: block;
    width: 70.83333333%;
}

.is-push-17 {
    left: 70.83333333%;
}

.is-pull-17 {
    right: 70.83333333%;
}

.is-offset-17 {
    margin-left: 70.83333333%;
}

.is-order-17 {
    -webkit-box-ordinal-group: 18;
    -ms-flex-order: 17;
    order: 17;
}

.is-col-16 {
    display: block;
    width: 66.66666667%;
}

.is-push-16 {
    left: 66.66666667%;
}

.is-pull-16 {
    right: 66.66666667%;
}

.is-offset-16 {
    margin-left: 66.66666667%;
}

.is-order-16 {
    -webkit-box-ordinal-group: 17;
    -ms-flex-order: 16;
    order: 16;
}

.is-col-15 {
    display: block;
    width: 62.5%;
}

.is-push-15 {
    left: 62.5%;
}

.is-pull-15 {
    right: 62.5%;
}

.is-offset-15 {
    margin-left: 62.5%;
}

.is-order-15 {
    -webkit-box-ordinal-group: 16;
    -ms-flex-order: 15;
    order: 15;
}

.is-col-14 {
    display: block;
    width: 58.33333333%;
}

.is-push-14 {
    left: 58.33333333%;
}

.is-pull-14 {
    right: 58.33333333%;
}

.is-offset-14 {
    margin-left: 58.33333333%;
}

.is-order-14 {
    -webkit-box-ordinal-group: 15;
    -ms-flex-order: 14;
    order: 14;
}

.is-col-13 {
    display: block;
    width: 54.16666667%;
}

.is-push-13 {
    left: 54.16666667%;
}

.is-pull-13 {
    right: 54.16666667%;
}

.is-offset-13 {
    margin-left: 54.16666667%;
}

.is-order-13 {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13;
}

.is-col-12 {
    display: block;
    width: 50%;
}

.is-push-12 {
    left: 50%;
}

.is-pull-12 {
    right: 50%;
}

.is-offset-12 {
    margin-left: 50%;
}

.is-order-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12;
}

.is-col-11 {
    display: block;
    width: 45.83333333%;
}

.is-push-11 {
    left: 45.83333333%;
}

.is-pull-11 {
    right: 45.83333333%;
}

.is-offset-11 {
    margin-left: 45.83333333%;
}

.is-order-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11;
}

.is-col-10 {
    display: block;
    width: 41.66666667%;
}

.is-push-10 {
    left: 41.66666667%;
}

.is-pull-10 {
    right: 41.66666667%;
}

.is-offset-10 {
    margin-left: 41.66666667%;
}

.is-order-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10;
}

.is-col-9 {
    display: block;
    width: 37.5%;
}

.is-push-9 {
    left: 37.5%;
}

.is-pull-9 {
    right: 37.5%;
}

.is-offset-9 {
    margin-left: 37.5%;
}

.is-order-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9;
}

.is-col-8 {
    display: block;
    width: 33.33333333%;
}

.is-push-8 {
    left: 33.33333333%;
}

.is-pull-8 {
    right: 33.33333333%;
}

.is-offset-8 {
    margin-left: 33.33333333%;
}

.is-order-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8;
}

.is-col-7 {
    display: block;
    width: 29.16666667%;
}

.is-push-7 {
    left: 29.16666667%;
}

.is-pull-7 {
    right: 29.16666667%;
}

.is-offset-7 {
    margin-left: 29.16666667%;
}

.is-order-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7;
}

.is-col-6 {
    display: block;
    width: 25%;
}

.is-push-6 {
    left: 25%;
}

.is-pull-6 {
    right: 25%;
}

.is-offset-6 {
    margin-left: 25%;
}

.is-order-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6;
}

.is-col-5 {
    display: block;
    width: 20.83333333%;
}

.is-push-5 {
    left: 20.83333333%;
}

.is-pull-5 {
    right: 20.83333333%;
}

.is-offset-5 {
    margin-left: 20.83333333%;
}

.is-order-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5;
}

.is-col-4 {
    display: block;
    width: 16.66666667%;
}

.is-push-4 {
    left: 16.66666667%;
}

.is-pull-4 {
    right: 16.66666667%;
}

.is-offset-4 {
    margin-left: 16.66666667%;
}

.is-order-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4;
}

.is-col-3 {
    display: block;
    width: 12.5%;
}

.is-push-3 {
    left: 12.5%;
}

.is-pull-3 {
    right: 12.5%;
}

.is-offset-3 {
    margin-left: 12.5%;
}

.is-order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
}

.is-col-2 {
    display: block;
    width: 8.33333333%;
}

.is-push-2 {
    left: 8.33333333%;
}

.is-pull-2 {
    right: 8.33333333%;
}

.is-offset-2 {
    margin-left: 8.33333333%;
}

.is-order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
}

.is-col-1 {
    display: block;
    width: 4.16666667%;
}

.is-push-1 {
    left: 4.16666667%;
}

.is-pull-1 {
    right: 4.16666667%;
}

.is-offset-1 {
    margin-left: 4.16666667%;
}

.is-order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
}

.is-col-0 {
    display: none;
}

.is-push-0 {
    left: auto;
}

.is-pull-0 {
    right: auto;
}

/* 间隙 */
/* 间隙 */
.has-space-mg-1 {
    margin-left: -2 rpx !important;
    margin-right: -2 rpx !important;
}

.has-space-pd-1 {
    padding-left: 2 rpx !important;
    padding-right: 2 rpx !important;
}

.has-space-mg-2 {
    margin-left: -4 rpx !important;
    margin-right: -4 rpx !important;
}

.has-space-pd-2 {
    padding-left: 4 rpx !important;
    padding-right: 4 rpx !important;
}

.has-space-mg-3 {
    margin-left: -6 rpx !important;
    margin-right: -6 rpx !important;
}

.has-space-pd-3 {
    padding-left: 6 rpx !important;
    padding-right: 6 rpx !important;
}

.has-space-mg-5 {
    margin-left: -10 rpx !important;
    margin-right: -10 rpx !important;
}

.has-space-pd-5 {
    padding-left: 10 rpx !important;
    padding-right: 10 rpx !important;
}

.has-space-mg-7 {
    margin-left: -14 rpx !important;
    margin-right: -14 rpx !important;
}

.has-space-pd-7 {
    padding-left: 14 rpx !important;
    padding-right: 14 rpx !important;
}

.has-space-mg-8 {
    margin-left: -16 rpx !important;
    margin-right: -16 rpx !important;
}

.has-space-pd-8 {
    padding-left: 16 rpx !important;
    padding-right: 16 rpx !important;
}

.has-space-mg-10 {
    margin-left: -20 rpx !important;
    margin-right: -20 rpx !important;
}

.has-space-pd-10 {
    padding-left: 20 rpx !important;
    padding-right: 20 rpx !important;
}

.has-space-mg-15 {
    margin-left: -30 rpx !important;
    margin-right: -30 rpx !important;
}

.has-space-pd-15 {
    padding-left: 30 rpx !important;
    padding-right: 30 rpx !important;
}

.has-space-mg-20 {
    margin-left: -40 rpx !important;
    margin-right: -40 rpx !important;
}

.has-space-pd-20 {
    padding-left: 40 rpx !important;
    padding-right: 40 rpx !important;
}

.has-space-mg-25 {
    margin-left: -50 rpx !important;
    margin-right: -50 rpx !important;
}

.has-space-pd-25 {
    padding-left: 50 rpx !important;
    padding-right: 50 rpx !important;
}

.has-space-mg-30 {
    margin-left: -60 rpx !important;
    margin-right: -60 rpx !important;
}

.has-space-pd-30 {
    padding-left: 60 rpx !important;
    padding-right: 60 rpx !important;
}

.has-space-mg-35 {
    margin-left: -70 rpx !important;
    margin-right: -70 rpx !important;
}

.has-space-pd-35 {
    padding-left: 70 rpx !important;
    padding-right: 70 rpx !important;
}

.has-space-mg-40 {
    margin-left: -80 rpx !important;
    margin-right: -80 rpx !important;
}

.has-space-pd-40 {
    padding-left: 80 rpx !important;
    padding-right: 80 rpx !important;
}

.has-space-mg-45 {
    margin-left: -90 rpx !important;
    margin-right: -90 rpx !important;
}

.has-space-pd-45 {
    padding-left: 90 rpx !important;
    padding-right: 90 rpx !important;
}

.has-space-mg-50 {
    margin-left: -100 rpx !important;
    margin-right: -100 rpx !important;
}

.has-space-pd-50 {
    padding-left: 100 rpx !important;
    padding-right: 100 rpx !important;
}

.has-space-mg-55 {
    margin-left: -110 rpx !important;
    margin-right: -110 rpx !important;
}

.has-space-pd-55 {
    padding-left: 110 rpx !important;
    padding-right: 110 rpx !important;
}

.has-space-mg-60 {
    margin-left: -120 rpx !important;
    margin-right: -120 rpx !important;
}

.has-space-pd-60 {
    padding-left: 120 rpx !important;
    padding-right: 120 rpx !important;
}

/* 宫格 */
.s-grids {
    position: relative;
    overflow: hidden;
}

.is-grid:before {
    top: 0;
    width: 1px;
    border-right: 1px solid #dcdee2;
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}

.is-grid::after,
.is-grid::before {
    content: " ";
    position: absolute;
    right: 0;
    bottom: 0;
    color: #dcdee2;
}

.is-grid::after {
    left: 0;
    height: 1px;
    border-bottom: 1px solid #dcdee2;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.s-grids-noborder {
    position: relative;
    overflow: hidden;
}

.s-grids::before {
    right: 0;
    height: 1px;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.s-grids::after,
.s-grids::before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    color: #dcdee2;
}

.s-grids::after {
    width: 1px;
    bottom: 0;
    /* border-left: 1px solid #dcdee2; */
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}

.is-grid {
    position: relative;
    float: left;
    box-sizing: border-box;
}

.is-grid-2 {
    width: 50%;
}

.is-grid-3 {
    width: 33.33333333%;
}

.is-grid-4 {
    width: 25%;
}

.is-grid-5 {
    width: 20%;
}

/* 模拟 a 的点击效果 */
a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    text-decoration: none;
}

.is-a {
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    color: inherit;
}

.is-a:active {
    background-color: #ececec;
}

.has-underline {
    text-decoration: underline;
}

.is-red {
    color: #e64340 !important;
}

.has-bg-red {
    background-color: #e64340 !important;
}

.is-grey {
    color: #888888 !important;
}

.has-bg-grey {
    background-color: #888888 !important;
}

.is-green {
    color: #09bb07 !important;
}

.has-bg-green {
    background-color: #09bb07 !important;
}

.is-blue {
    color: #2A62FF !important;
}

.has-bg-blue {
    background-color: #2A62FF !important;
}

.is-black {
    color: black !important;
}

.has-bg-black {
    background-color: black !important;
}

.is-white {
    color: #fff !important;
}

.has-bg-white {
    background-color: #fff !important;
}

.has-title-color {
    color: black;
}

.has-content-color {
    color: #353535;
}

.has-desc-color {
    color: #888888;
}

.has-link-color {
    color: #576b95;
}

.is-normal {
    font-weight: normal;
}

.is-light {
    font-weight: 300;
}

.is-bold {
    font-weight: 700 !important;
}

.is-italic {
    font-style: italic;
}

.is-left {
    text-align: left !important;
}

.is-oneline {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.is-right {
    text-align: center !important;
}

/* 字体居中 */
.is-center {
    text-align: center !important;
}

.is-p {
    font-size: 32 rpx;
    color: #353535;
    line-height: 2;
    margin-bottom: 30 rpx;
    text-align: justify;
}

.is-h1,
.is-h2,
.is-h3,
.is-h4,
.is-h5,
.is-h6 {
    color: black;
    font-weight: normal;
}

.is-h1 {
    font-size: 48 rpx !important;
}

.is-h2 {
    font-size: 44 rpx !important;
}

.is-h3 {
    font-size: 36 rpx !important;
}

.is-h4 {
    font-size: 32 rpx !important;
}

.is-h5 {
    font-size: 28 rpx !important;
}

.is-h6 {
    font-size: 24 rpx !important;
}

/* 排版容器，小程序可以不用，正文排版等，请在容器上添加此类，自动格式化 */
.s-typo p {
    font-size: 32 rpx;
    color: #353535;
    line-height: 2;
    margin-bottom: 30 rpx;
    text-align: justify;
}

.s-typo h1,
.s-typo h2,
.s-typo h3,
.s-typo h4,
.s-typo h5,
.s-typo h6 {
    color: black;
    font-weight: normal;
}

.s-typo h1 {
    font-size: 48 rpx;
}

.s-typo h2 {
    font-size: 44 rpx;
}

.s-typo h3 {
    font-size: 36 rpx;
}

.s-typo h4 {
    font-size: 32 rpx;
}

.s-typo h5 {
    font-size: 28 rpx;
}

.s-typo h6 {
    font-size: 24 rpx;
}

.s-typo ol li {
    list-style-type: decimal;
    margin-left: 1rem;
    line-height: 2;
}

.s-typo ul li {
    list-style-type: disc;
    margin-left: 1rem;
    line-height: 2;
}

.s-typo img {
    display: inline-block;
    height: auto;
    max-width: 100%;
}

/* 辅助类 */
/* 页面高度 */
.is-100vh {
    height: 100vh;
}

.is-33vh {
    height: 33vh;
}

.is-50vh {
    height: 50vh;
}

.is-20vh {
    height: 20vh;
}

/*页面宽度*/
.is-width-30 {
    width: 30% !important;
}

.is-width-40 {
    width: 40% !important;
}

.is-width-50 {
    width: 50% !important;
}

.is-width-60 {
    width: 60% !important;
}

.is-width-70 {
    width: 70% !important;
}

.is-width-80 {
    width: 80% !important;
}

.is-width-90 {
    width: 90% !important;
}

.is-width-100 {
    width: 100% !important;
}

.is-width-100px {
    width: 200 rpx !important;
}

.is-width-130px {
    width: 260 rpx !important;
}

.is-width-150px {
    width: 300 rpx !important;
}

.is-width-180px {
    width: 360 rpx !important;
}

.is-width-200px {
    width: 400 rpx !important;
}

.is-width-220px {
    width: 440 rpx !important;
}

/* 图片 */
.is-img {
    display: block;
}

/* 图片响应式 小程序的兼容 mode='widthFix' */
.is-response {
    display: block;
    width: 100%;
    max-width: 100%;
    height: 500upx;
}

.has-floatr {
    float: right;
}

.has-floatl {
    float: left;
}

.is-absolute {
    position: absolute;
}

.is-relative {
    position: relative;
}

.is-fixed {
    position: fixed;
}

.has-right0 {
    right: 0;
}

.has-left0 {
    left: 0;
}

.has-top0 {
    top: 0;
}

.hsa-bottom0 {
    bottom: 0;
}

/* 圆角 */
.is-circle {
    border-radius: 50%;
}

/* 行高 */
.is-lh-1 {
    line-height: 1 !important;
}

.is-lh-15 {
    line-height: 1.5 !important;
}

.is-lh-16 {
    line-height: 1.6 !important;
}

.is-lh-18 {
    line-height: 1.8 !important;
}

.is-lh-2 {
    line-height: 2 !important;
}

.is-lh-25 {
    line-height: 2.5 !important;
}

/* 字体大小 */
.is-size-12 {
    font-size: 24 rpx !important;
}

.is-size-14 {
    font-family: "Franklin Gothic Medium";
    font-size: 28 rpx !important;
    color: #333333;
}

.is-size-16 {
    font-size: 32 rpx !important;
}

.is-size-17 {
    font-size: 34 rpx !important;
}

.is-size-18 {
    font-size: 36 rpx !important;
}

.is-size-20 {
    font-size: 40 rpx !important;
}

.is-size-25 {
    font-size: 50 rpx !important;
}

.is-size-30 {
    font-size: 60 rpx !important;
}

.is-size-35 {
    font-size: 70 rpx !important;
}

.is-size-40 {
    font-size: 80 rpx !important;
}

.is-size-50 {
    font-size: 100 rpx !important;
}

.is-size-60 {
    font-size: 120 rpx !important;
}

/* 徽标 */
.has-badge-border {
    border: 1px solid #dcdee2;
    padding: 3px 3px;
}

.has-radius {
    border-radius: 8 rpx;
}

.has-radius-0 {
    border-radius: 0 rpx;
}

.has-radius-2 {
    border-radius: 4 rpx;
}

.has-radius-top-2 {
    border-top-left-radius: 4 rpx;
    border-top-right-radius: 4 rpx;
}

.has-radius-4 {
    border-radius: 8 rpx;
}

.has-radius-top-4 {
    border-top-left-radius: 8 rpx;
    border-top-right-radius: 8 rpx;
}

.has-radius-6 {
    border-radius: 12 rpx;
}

.has-radius-8 {
    border-radius: 16 rpx;
}

/* 1px 边框 */
.has-borderb:before {
    border-bottom: 1px solid #dcdee2;
    content: '';
    display: block;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    -webkit-transform-origin: left bottom;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-borderb:before {
        -webkit-transform: scaleY(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-borderb:before {
        -webkit-transform: scaleY(0.3333);
    }
}

.has-bordert:before {
    border-top: 1px solid #dcdee2;
    content: '';
    display: block;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transform-origin: left top;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-bordert:before {
        -webkit-transform: scaleY(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-bordert:before {
        -webkit-transform: scaleY(0.3333);
    }
}

.has-borderl:before {
    border-left: 1px solid #dcdee2;
    content: '';
    display: block;
    bottom: 0;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transform-origin: left top;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-borderl:before {
        -webkit-transform: scaleX(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-borderl:before {
        -webkit-transform: scaleX(0.3333);
    }
}

.has-borderr:before {
    border-right: 1px solid #dcdee2;
    content: '';
    display: block;
    bottom: 0;
    position: absolute;
    right: 0;
    top: 0;
    -webkit-transform-origin: right top;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-borderr:before {
        -webkit-transform: scaleX(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-borderr:before {
        -webkit-transform: scaleX(0.3333);
    }
}

.has-bordert,
.has-borderl,
.has-borderb,
.has-borderr,
.has-bordertb,
.has-bordera,
.has-border-radius {
    position: relative;
}

.has-bordertb:before {
    border-top: 1px solid #dcdee2;
    content: '';
    display: block;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transform-origin: left top;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-bordertb:before {
        -webkit-transform: scaleY(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-bordertb:before {
        -webkit-transform: scaleY(0.3333);
    }
}

.has-bordertb:after {
    border-bottom: 1px solid #dcdee2;
    content: '';
    display: block;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    -webkit-transform-origin: left bottom;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-bordertb:after {
        -webkit-transform: scaleY(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-bordertb:after {
        -webkit-transform: scaleY(0.3333);
    }
}

.has-bordera:before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid #dcdee2;
    -webkit-transform-origin: 0 0;
    padding: 1px;
    -webkit-box-sizing: border-box;
    pointer-events: none;
    z-index: 10;
    pointer-events: none;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-bordera:before {
        width: 200%;
        height: 200%;
        -webkit-transform: scale(0.5);
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-bordera:before {
        width: 300%;
        height: 300%;
        -webkit-transform: scale(0.3333);
    }
}

.has-border-radius:before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid #dcdee2;
    -webkit-transform-origin: 0 0;
    padding: 1px;
    -webkit-box-sizing: border-box;
    border-radius: 4px;
    pointer-events: none;
    z-index: 10;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .has-border-radius:before {
        width: 200%;
        height: 200%;
        -webkit-transform: scale(0.5);
        border-radius: 8px;
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
    .has-border-radius:before {
        width: 300%;
        height: 300%;
        -webkit-transform: scale(0.3333);
        border-radius: 12px;
    }
}

/* 浮动 */
.has-mg-0 {
    margin: 0 rpx !important;
}

.has-mgtb-0 {
    margin-top: 0 rpx !important;
    margin-bottom: 0 rpx !important;
}

.has-mglr-0 {
    margin-left: 0 rpx !important;
    margin-right: 0 rpx !important;
}

.has-pd-0 {
    padding: 0 rpx !important;
}

.has-pdtb-0 {
    padding-top: 0 rpx !important;
    padding-bottom: 0 rpx !important;
}

.has-pdlr-0 {
    padding-left: 0 rpx !important;
    padding-right: 0 rpx !important;
}

.has-mgt-0 {
    margin-top: 0 rpx !important;
}

.has-mgl-0 {
    margin-left: 0 rpx !important;
}

.has-mgr-0 {
    margin-right: 0 rpx !important;
}

.has-mgb-0 {
    margin-bottom: 0 rpx !important;
}

.has-pdt-0 {
    padding-top: 0 rpx !important;
}

.has-pdl-0 {
    padding-left: 0 rpx !important;
}

.has-pdr-0 {
    padding-right: 0 rpx !important;
}

.has-pdb-0 {
    padding-bottom: 0 rpx !important;
}

.has-mg-1 {
    margin: 2 rpx !important;
}

.has-mgtb-1 {
    margin-top: 2 rpx !important;
    margin-bottom: 2 rpx !important;
}

.has-mglr-1 {
    margin-left: 2 rpx !important;
    margin-right: 2 rpx !important;
}

.has-pd-1 {
    padding: 2 rpx !important;
}

.has-pdtb-1 {
    padding-top: 2 rpx !important;
    padding-bottom: 2 rpx !important;
}

.has-pdlr-1 {
    padding-left: 2 rpx !important;
    padding-right: 2 rpx !important;
}

.has-mgt-1 {
    margin-top: 2 rpx !important;
}

.has-mgl-1 {
    margin-left: 2 rpx !important;
}

.has-mgr-1 {
    margin-right: 2 rpx !important;
}

.has-mgb-1 {
    margin-bottom: 2 rpx !important;
}

.has-pdt-1 {
    padding-top: 2 rpx !important;
}

.has-pdl-1 {
    padding-left: 2 rpx !important;
}

.has-pdr-1 {
    padding-right: 2 rpx !important;
}

.has-pdb-1 {
    padding-bottom: 2 rpx !important;
}

.has-mg-2 {
    margin: 4 rpx !important;
}

.has-mgtb-2 {
    margin-top: 4 rpx !important;
    margin-bottom: 4 rpx !important;
}

.has-mglr-2 {
    margin-left: 4 rpx !important;
    margin-right: 4 rpx !important;
}

.has-pd-2 {
    padding: 4 rpx !important;
}

.has-pdtb-2 {
    padding-top: 4 rpx !important;
    padding-bottom: 4 rpx !important;
}

.has-pdlr-2 {
    padding-left: 4 rpx !important;
    padding-right: 4 rpx !important;
}

.has-mgt-2 {
    margin-top: 4 rpx !important;
}

.has-mgl-2 {
    margin-left: 4 rpx !important;
}

.has-mgr-2 {
    margin-right: 4 rpx !important;
}

.has-mgb-2 {
    margin-bottom: 4 rpx !important;
}

.has-pdt-2 {
    padding-top: 4 rpx !important;
}

.has-pdl-2 {
    padding-left: 4 rpx !important;
}

.has-pdr-2 {
    padding-right: 4 rpx !important;
}

.has-pdb-2 {
    padding-bottom: 4 rpx !important;
}

.has-mg-3 {
    margin: 6 rpx !important;
}

.has-mgtb-3 {
    margin-top: 6 rpx !important;
    margin-bottom: 6 rpx !important;
}

.has-mglr-3 {
    margin-left: 6 rpx !important;
    margin-right: 6 rpx !important;
}

.has-pd-3 {
    padding: 6 rpx !important;
}

.has-pdtb-3 {
    padding-top: 6 rpx !important;
    padding-bottom: 6 rpx !important;
}

.has-pdlr-3 {
    padding-left: 6 rpx !important;
    padding-right: 6 rpx !important;
}

.has-mgt-3 {
    margin-top: 6 rpx !important;
}

.has-mgl-3 {
    margin-left: 6 rpx !important;
}

.has-mgr-3 {
    margin-right: 6 rpx !important;
}

.has-mgb-3 {
    margin-bottom: 6 rpx !important;
}

.has-pdt-3 {
    padding-top: 6 rpx !important;
}

.has-pdl-3 {
    padding-left: 6 rpx !important;
}

.has-pdr-3 {
    padding-right: 6 rpx !important;
}

.has-pdb-3 {
    padding-bottom: 6 rpx !important;
}

.has-mg-4 {
    margin: 8 rpx !important;
}

.has-mgtb-4 {
    margin-top: 8 rpx !important;
    margin-bottom: 8 rpx !important;
}

.has-mglr-4 {
    margin-left: 8 rpx !important;
    margin-right: 8 rpx !important;
}

.has-pd-4 {
    padding: 8 rpx !important;
}

.has-pdtb-4 {
    padding-top: 8 rpx !important;
    padding-bottom: 8 rpx !important;
}

.has-pdlr-4 {
    padding-left: 8 rpx !important;
    padding-right: 8 rpx !important;
}

.has-mgt-4 {
    margin-top: 8 rpx !important;
}

.has-mgl-4 {
    margin-left: 8 rpx !important;
}

.has-mgr-4 {
    margin-right: 8 rpx !important;
}

.has-mgb-4 {
    margin-bottom: 8 rpx !important;
}

.has-pdt-4 {
    padding-top: 8 rpx !important;
}

.has-pdl-4 {
    padding-left: 8 rpx !important;
}

.has-pdr-4 {
    padding-right: 8 rpx !important;
}

.has-pdb-4 {
    padding-bottom: 8 rpx !important;
}

.has-mg-5 {
    margin: 10 rpx !important;
}

.has-mgtb-5 {
    margin-top: 10 rpx !important;
    margin-bottom: 10 rpx !important;
}

.has-mglr-5 {
    margin-left: 10 rpx !important;
    margin-right: 10 rpx !important;
}

.has-pd-5 {
    padding: 10 rpx !important;
}

.has-pdtb-5 {
    padding-top: 10 rpx !important;
    padding-bottom: 10 rpx !important;
}

.has-pdlr-5 {
    padding-left: 10 rpx !important;
    padding-right: 10 rpx !important;
}

.has-mgt-5 {
    margin-top: 10 rpx !important;
}

.has-mgl-5 {
    margin-left: 10 rpx !important;
}

.has-mgr-5 {
    margin-right: 10 rpx !important;
}

.has-mgb-5 {
    margin-bottom: 10 rpx !important;
}

.has-pdt-5 {
    padding-top: 10 rpx !important;
}

.has-pdl-5 {
    padding-left: 10 rpx !important;
}

.has-pdr-5 {
    padding-right: 10 rpx !important;
}

.has-pdb-5 {
    padding-bottom: 10 rpx !important;
}

.has-mg-6 {
    margin: 12 rpx !important;
}

.has-mgtb-6 {
    margin-top: 12 rpx !important;
    margin-bottom: 12 rpx !important;
}

.has-mglr-6 {
    margin-left: 12 rpx !important;
    margin-right: 12 rpx !important;
}

.has-pd-6 {
    padding: 12 rpx !important;
}

.has-pdtb-6 {
    padding-top: 12 rpx !important;
    padding-bottom: 12 rpx !important;
}

.has-pdlr-6 {
    padding-left: 12 rpx !important;
    padding-right: 12 rpx !important;
}

.has-mgt-6 {
    margin-top: 12 rpx !important;
}

.has-mgl-6 {
    margin-left: 12 rpx !important;
}

.has-mgr-6 {
    margin-right: 12 rpx !important;
}

.has-mgb-6 {
    margin-bottom: 12 rpx !important;
}

.has-pdt-6 {
    padding-top: 12 rpx !important;
}

.has-pdl-6 {
    padding-left: 12 rpx !important;
}

.has-pdr-6 {
    padding-right: 12 rpx !important;
}

.has-pdb-6 {
    padding-bottom: 12 rpx !important;
}

.has-mg-7 {
    margin: 14 rpx !important;
}

.has-mgtb-7 {
    margin-top: 14 rpx !important;
    margin-bottom: 14 rpx !important;
}

.has-mglr-7 {
    margin-left: 14 rpx !important;
    margin-right: 14 rpx !important;
}

.has-pd-7 {
    padding: 14 rpx !important;
}

.has-pdtb-7 {
    padding-top: 14 rpx !important;
    padding-bottom: 14 rpx !important;
}

.has-pdlr-7 {
    padding-left: 14 rpx !important;
    padding-right: 14 rpx !important;
}

.has-mgt-7 {
    margin-top: 14 rpx !important;
}

.has-mgl-7 {
    margin-left: 14 rpx !important;
}

.has-mgr-7 {
    margin-right: 14 rpx !important;
}

.has-mgb-7 {
    margin-bottom: 14 rpx !important;
}

.has-pdt-7 {
    padding-top: 14 rpx !important;
}

.has-pdl-7 {
    padding-left: 14 rpx !important;
}

.has-pdr-7 {
    padding-right: 14 rpx !important;
}

.has-pdb-7 {
    padding-bottom: 14 rpx !important;
}

.has-mg-8 {
    margin: 16 rpx !important;
}

.has-mgtb-8 {
    margin-top: 16 rpx !important;
    margin-bottom: 16 rpx !important;
}

.has-mglr-8 {
    margin-left: 16 rpx !important;
    margin-right: 16 rpx !important;
}

.has-pd-8 {
    padding: 16 rpx !important;
}

.has-pdtb-8 {
    padding-top: 16 rpx !important;
    padding-bottom: 16 rpx !important;
}

.has-pdlr-8 {
    padding-left: 16 rpx !important;
    padding-right: 16 rpx !important;
}

.has-mgt-8 {
    margin-top: 16 rpx !important;
}

.has-mgl-8 {
    margin-left: 16 rpx !important;
}

.has-mgr-8 {
    margin-right: 16 rpx !important;
}

.has-mgb-8 {
    margin-bottom: 16 rpx !important;
}

.has-pdt-8 {
    padding-top: 16 rpx !important;
}

.has-pdl-8 {
    padding-left: 16 rpx !important;
}

.has-pdr-8 {
    padding-right: 16 rpx !important;
}

.has-pdb-8 {
    padding-bottom: 16 rpx !important;
}

.has-mg-10 {
    margin: 20 rpx !important;
}

.has-mgtb-10 {
    margin-top: 20 rpx !important;
    margin-bottom: 20 rpx !important;
}

.has-mglr-10 {
    margin-left: 20 rpx !important;
    margin-right: 20 rpx !important;
}

.has-pd-10 {
    padding: 20 rpx !important;
}

.has-pdtb-10 {
    padding-top: 20 rpx !important;
    padding-bottom: 20 rpx !important;
}

.has-pdlr-10 {
    padding-left: 20 rpx !important;
    padding-right: 20 rpx !important;
}

.has-mgt-10 {
    margin-top: 20 rpx !important;
}

.has-mgl-10 {
    margin-left: 20 rpx !important;
}

.has-mgr-10 {
    margin-right: 20 rpx !important;
}

.has-mgb-10 {
    margin-bottom: 20 rpx !important;
}

.has-pdt-10 {
    padding-top: 20 rpx !important;
}

.has-pdl-10 {
    padding-left: 20 rpx !important;
}

.has-pdr-10 {
    padding-right: 20 rpx !important;
}

.has-pdb-10 {
    padding-bottom: 20 rpx !important;
}

.has-mg-12 {
    margin: 24 rpx !important;
}

.has-mgtb-12 {
    margin-top: 24 rpx !important;
    margin-bottom: 24 rpx !important;
}

.has-mglr-12 {
    margin-left: 24 rpx !important;
    margin-right: 24 rpx !important;
}

.has-pd-12 {
    padding: 24 rpx !important;
}

.has-pdtb-12 {
    padding-top: 24 rpx !important;
    padding-bottom: 24 rpx !important;
}

.has-pdlr-12 {
    padding-left: 24 rpx !important;
    padding-right: 24 rpx !important;
}

.has-mgt-12 {
    margin-top: 24 rpx !important;
}

.has-mgl-12 {
    margin-left: 24 rpx !important;
}

.has-mgr-12 {
    margin-right: 24 rpx !important;
}

.has-mgb-12 {
    margin-bottom: 24 rpx !important;
}

.has-pdt-12 {
    padding-top: 24 rpx !important;
}

.has-pdl-12 {
    padding-left: 24 rpx !important;
}

.has-pdr-12 {
    padding-right: 24 rpx !important;
}

.has-pdb-12 {
    padding-bottom: 24 rpx !important;
}

.has-mg-15 {
    margin: 30 rpx !important;
}

.has-mgtb-15 {
    margin-top: 30 rpx !important;
    margin-bottom: 30 rpx !important;
}

.has-mglr-15 {
    margin-left: 30 rpx !important;
    margin-right: 30 rpx !important;
}

.has-pd-15 {
    padding: 30 rpx !important;
}

.has-pdtb-15 {
    padding-top: 30 rpx !important;
    padding-bottom: 30 rpx !important;
}

.has-pdlr-15 {
    padding-left: 30 rpx !important;
    padding-right: 30 rpx !important;
}

.has-mgt-15 {
    margin-top: 30 rpx !important;
}

.has-mgl-15 {
    margin-left: 30 rpx !important;
}

.has-mgr-15 {
    margin-right: 30 rpx !important;
}

.has-mgb-15 {
    margin-bottom: 30 rpx !important;
}

.has-pdt-15 {
    padding-top: 30 rpx !important;
}

.has-pdl-15 {
    padding-left: 30 rpx !important;
}

.has-pdr-15 {
    padding-right: 30 rpx !important;
}

.has-pdb-15 {
    padding-bottom: 30 rpx !important;
}

.has-mg-18 {
    margin: 36 rpx !important;
}

.has-mgtb-18 {
    margin-top: 36 rpx !important;
    margin-bottom: 36 rpx !important;
}

.has-mglr-18 {
    margin-left: 36 rpx !important;
    margin-right: 36 rpx !important;
}

.has-pd-18 {
    padding: 36 rpx !important;
}

.has-pdtb-18 {
    padding-top: 36 rpx !important;
    padding-bottom: 36 rpx !important;
}

.has-pdlr-18 {
    padding-left: 36 rpx !important;
    padding-right: 36 rpx !important;
}

.has-mgt-18 {
    margin-top: 36 rpx !important;
}

.has-mgl-18 {
    margin-left: 36 rpx !important;
}

.has-mgr-18 {
    margin-right: 36 rpx !important;
}

.has-mgb-18 {
    margin-bottom: 36 rpx !important;
}

.has-pdt-18 {
    padding-top: 36 rpx !important;
}

.has-pdl-18 {
    padding-left: 36 rpx !important;
}

.has-pdr-18 {
    padding-right: 36 rpx !important;
}

.has-pdb-18 {
    padding-bottom: 36 rpx !important;
}

.has-mg-20 {
    margin: 40 rpx !important;
}

.has-mgtb-20 {
    margin-top: 40 rpx !important;
    margin-bottom: 40 rpx !important;
}

.has-mglr-20 {
    margin-left: 40 rpx !important;
    margin-right: 40 rpx !important;
}

.has-pd-20 {
    padding: 40 rpx !important;
}

.has-pdtb-20 {
    padding-top: 40 rpx !important;
    padding-bottom: 40 rpx !important;
}

.has-pdlr-20 {
    padding-left: 40 rpx !important;
    padding-right: 40 rpx !important;
}

.has-mgt-20 {
    margin-top: 40 rpx !important;
}

.has-mgl-20 {
    margin-left: 40 rpx !important;
}

.has-mgr-20 {
    margin-right: 40 rpx !important;
}

.has-mgb-20 {
    margin-bottom: 40 rpx !important;
}

.has-pdt-20 {
    padding-top: 40 rpx !important;
}

.has-pdl-20 {
    padding-left: 40 rpx !important;
}

.has-pdr-20 {
    padding-right: 40 rpx !important;
}

.has-pdb-20 {
    padding-bottom: 40 rpx !important;
}

.has-mg-25 {
    margin: 50 rpx !important;
}

.has-mgtb-25 {
    margin-top: 50 rpx !important;
    margin-bottom: 50 rpx !important;
}

.has-mglr-25 {
    margin-left: 50 rpx !important;
    margin-right: 50 rpx !important;
}

.has-pd-25 {
    padding: 50 rpx !important;
}

.has-pdtb-25 {
    padding-top: 50 rpx !important;
    padding-bottom: 50 rpx !important;
}

.has-pdlr-25 {
    padding-left: 50 rpx !important;
    padding-right: 50 rpx !important;
}

.has-mgt-25 {
    margin-top: 50 rpx !important;
}

.has-mgl-25 {
    margin-left: 50 rpx !important;
}

.has-mgr-25 {
    margin-right: 50 rpx !important;
}

.has-mgb-25 {
    margin-bottom: 50 rpx !important;
}

.has-pdt-25 {
    padding-top: 50 rpx !important;
}

.has-pdl-25 {
    padding-left: 50 rpx !important;
}

.has-pdr-25 {
    padding-right: 50 rpx !important;
}

.has-pdb-25 {
    padding-bottom: 50 rpx !important;
}

.has-mg-30 {
    margin: 60 rpx !important;
}

.has-mgtb-30 {
    margin-top: 60 rpx !important;
    margin-bottom: 60 rpx !important;
}

.has-mglr-30 {
    margin-left: 60 rpx !important;
    margin-right: 60 rpx !important;
}

.has-pd-30 {
    padding: 60 rpx !important;
}

.has-pdtb-30 {
    padding-top: 60 rpx !important;
    padding-bottom: 60 rpx !important;
}

.has-pdlr-30 {
    padding-left: 60 rpx !important;
    padding-right: 60 rpx !important;
}

.has-mgt-30 {
    margin-top: 60 rpx !important;
}

.has-mgl-30 {
    margin-left: 60 rpx !important;
}

.has-mgr-30 {
    margin-right: 60 rpx !important;
}

.has-mgb-30 {
    margin-bottom: 60 rpx !important;
}

.has-pdt-30 {
    padding-top: 60 rpx !important;
}

.has-pdl-30 {
    padding-left: 60 rpx !important;
}

.has-pdr-30 {
    padding-right: 60 rpx !important;
}

.has-pdb-30 {
    padding-bottom: 60 rpx !important;
}

.has-mg-35 {
    margin: 70 rpx !important;
}

.has-mgtb-35 {
    margin-top: 70 rpx !important;
    margin-bottom: 70 rpx !important;
}

.has-mglr-35 {
    margin-left: 70 rpx !important;
    margin-right: 70 rpx !important;
}

.has-pd-35 {
    padding: 70 rpx !important;
}

.has-pdtb-35 {
    padding-top: 70 rpx !important;
    padding-bottom: 70 rpx !important;
}

.has-pdlr-35 {
    padding-left: 70 rpx !important;
    padding-right: 70 rpx !important;
}

.has-mgt-35 {
    margin-top: 70 rpx !important;
}

.has-mgl-35 {
    margin-left: 70 rpx !important;
}

.has-mgr-35 {
    margin-right: 70 rpx !important;
}

.has-mgb-35 {
    margin-bottom: 70 rpx !important;
}

.has-pdt-35 {
    padding-top: 70 rpx !important;
}

.has-pdl-35 {
    padding-left: 70 rpx !important;
}

.has-pdr-35 {
    padding-right: 70 rpx !important;
}

.has-pdb-35 {
    padding-bottom: 70 rpx !important;
}

.has-mg-40 {
    margin: 80 rpx !important;
}

.has-mgtb-40 {
    margin-top: 80 rpx !important;
    margin-bottom: 80 rpx !important;
}

.has-mglr-40 {
    margin-left: 80 rpx !important;
    margin-right: 80 rpx !important;
}

.has-pd-40 {
    padding: 80 rpx !important;
}

.has-pdtb-40 {
    padding-top: 80 rpx !important;
    padding-bottom: 80 rpx !important;
}

.has-pdlr-40 {
    padding-left: 80 rpx !important;
    padding-right: 80 rpx !important;
}

.has-mgt-40 {
    margin-top: 80 rpx !important;
}

.has-mgl-40 {
    margin-left: 80 rpx !important;
}

.has-mgr-40 {
    margin-right: 80 rpx !important;
}

.has-mgb-40 {
    margin-bottom: 80 rpx !important;
}

.has-pdt-40 {
    padding-top: 80 rpx !important;
}

.has-pdl-40 {
    padding-left: 80 rpx !important;
}

.has-pdr-40 {
    padding-right: 80 rpx !important;
}

.has-pdb-40 {
    padding-bottom: 80 rpx !important;
}

.has-mg-45 {
    margin: 90 rpx !important;
}

.has-mgtb-45 {
    margin-top: 90 rpx !important;
    margin-bottom: 90 rpx !important;
}

.has-mglr-45 {
    margin-left: 90 rpx !important;
    margin-right: 90 rpx !important;
}

.has-pd-45 {
    padding: 90 rpx !important;
}

.has-pdtb-45 {
    padding-top: 90 rpx !important;
    padding-bottom: 90 rpx !important;
}

.has-pdlr-45 {
    padding-left: 90 rpx !important;
    padding-right: 90 rpx !important;
}

.has-mgt-45 {
    margin-top: 90 rpx !important;
}

.has-mgl-45 {
    margin-left: 90 rpx !important;
}

.has-mgr-45 {
    margin-right: 90 rpx !important;
}

.has-mgb-45 {
    margin-bottom: 90 rpx !important;
}

.has-pdt-45 {
    padding-top: 90 rpx !important;
}

.has-pdl-45 {
    padding-left: 90 rpx !important;
}

.has-pdr-45 {
    padding-right: 90 rpx !important;
}

.has-pdb-45 {
    padding-bottom: 90 rpx !important;
}

.has-mg-50 {
    margin: 100 rpx !important;
}

.has-mgtb-50 {
    margin-top: 100 rpx !important;
    margin-bottom: 100 rpx !important;
}

.has-mglr-50 {
    margin-left: 100 rpx !important;
    margin-right: 100 rpx !important;
}

.has-pd-50 {
    padding: 100 rpx !important;
}

.has-pdtb-50 {
    padding-top: 100 rpx !important;
    padding-bottom: 100 rpx !important;
}

.has-pdlr-50 {
    padding-left: 100 rpx !important;
    padding-right: 100 rpx !important;
}

.has-mgt-50 {
    margin-top: 100 rpx !important;
}

.has-mgl-50 {
    margin-left: 100 rpx !important;
}

.has-mgr-50 {
    margin-right: 100 rpx !important;
}

.has-mgb-50 {
    margin-bottom: 100 rpx !important;
}

.has-pdt-50 {
    padding-top: 100 rpx !important;
}

.has-pdl-50 {
    padding-left: 100 rpx !important;
}

.has-pdr-50 {
    padding-right: 100 rpx !important;
}

.has-pdb-50 {
    padding-bottom: 100 rpx !important;
}

.has-mg-55 {
    margin: 110 rpx !important;
}

.has-mgtb-55 {
    margin-top: 110 rpx !important;
    margin-bottom: 110 rpx !important;
}

.has-mglr-55 {
    margin-left: 110 rpx !important;
    margin-right: 110 rpx !important;
}

.has-pd-55 {
    padding: 110 rpx !important;
}

.has-pdtb-55 {
    padding-top: 110 rpx !important;
    padding-bottom: 110 rpx !important;
}

.has-pdlr-55 {
    padding-left: 110 rpx !important;
    padding-right: 110 rpx !important;
}

.has-mgt-55 {
    margin-top: 110 rpx !important;
}

.has-mgl-55 {
    margin-left: 110 rpx !important;
}

.has-mgr-55 {
    margin-right: 110 rpx !important;
}

.has-mgb-55 {
    margin-bottom: 110 rpx !important;
}

.has-pdt-55 {
    padding-top: 110 rpx !important;
}

.has-pdl-55 {
    padding-left: 110 rpx !important;
}

.has-pdr-55 {
    padding-right: 110 rpx !important;
}

.has-pdb-55 {
    padding-bottom: 110 rpx !important;
}

.has-mg-60 {
    margin: 120 rpx !important;
}

.has-mgtb-60 {
    margin-top: 120 rpx !important;
    margin-bottom: 120 rpx !important;
}

.has-mglr-60 {
    margin-left: 120 rpx !important;
    margin-right: 120 rpx !important;
}

.has-pd-60 {
    padding: 120 rpx !important;
}

.has-pdtb-60 {
    padding-top: 120 rpx !important;
    padding-bottom: 120 rpx !important;
}

.has-pdlr-60 {
    padding-left: 120 rpx !important;
    padding-right: 120 rpx !important;
}

.has-mgt-60 {
    margin-top: 120 rpx !important;
}

.has-mgl-60 {
    margin-left: 120 rpx !important;
}

.has-mgr-60 {
    margin-right: 120 rpx !important;
}

.has-mgb-60 {
    margin-bottom: 120 rpx !important;
}

.has-pdt-60 {
    padding-top: 120 rpx !important;
}

.has-pdl-60 {
    padding-left: 120 rpx !important;
}

.has-pdr-60 {
    padding-right: 120 rpx !important;
}

.has-pdb-60 {
    padding-bottom: 120 rpx !important;
}

/* 按钮  */
.is-btn,
.is-btn-lg,
.is-btn-md {
    position: relative;
    text-align: center;
    background-color: #fff;
    vertical-align: top;
    color: #000;
    -webkit-box-sizing: border-box;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #dcdee2;
    border-radius: 6 rpx;
    text-decoration: none;
}

.is-btn:not(.disabled):not(:disabled):active,
.is-btn.active,
.is-btn-lg:not(.disabled):not(:disabled):active,
.is-btn-lg.active,
.is-btn-md:not(.disabled):not(:disabled):active,
.is-btn-md.active {
    background-color: #f0f0f0;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border-color: #dcdee2;
}

.is-btn.disabled,
.is-btn:disabled,
.is-btn-lg.disabled,
.is-btn-lg:disabled,
.is-btn-md.disabled,
.is-btn-md:disabled {
    border: 0;
    color: #bbb;
    background: #e9ebec;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.is-btn {
    height: 60 rpx;
    line-height: 60 rpx;
    padding: 0 rpx 32 rpx;
    display: block;
    text-align: center;
    font-size: 28 rpx;
    border-radius: 4 rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
}

.is-btn-md {
    display: block;
    text-align: center;
    width: 280 rpx;
    height: 80 rpx;
    line-height: 80 rpx;
    font-size: 34 rpx;
    border-radius: 6 rpx;
    margin: auto;
    margin-bottom: 30 rpx;
}

.is-btn-lg {
    font-size: 34 rpx;
    height: 80 rpx;
    line-height: 80 rpx;
    display: block;
    text-align: center;
    width: 100%;
    border-radius: 6 rpx;
    margin-bottom: 30 rpx;
}

.has-btn-radius {
    border-radius: 50px;
}

.has-bg-green {
    border: 0;
    background-color: #09bb07;
    color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.has-bg-green:not(.disabled):not(:disabled):active,
.has-bg-green.active {
    background: #179b16 !important;
    color: rgba(255, 255, 255, 0.6) !important;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.has-bg-blue {
    border: 0;
    background-color: #2A62FF;
    color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    box-shadow: 0 2px 6px #71b6f7;
}

.has-bg-blue:not(.disabled):not(:disabled):active,
.has-bg-blue.active {
    background: #0e80d2 !important;
    color: rgba(255, 255, 255, 0.6) !important;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    box-shadow: 0 2px 6px #71b6f7;
}

.has-bg-red {
    border: 0;
    background-color: #e64340;
    color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    box-shadow: 0 2px 6px #ffa299;
}

.has-bg-red:not(.disabled):not(:disabled):active,
.has-bg-red.active {
    background: #ce3c39 !important;
    color: rgba(255, 255, 255, 0.6) !important;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    box-shadow: 0 2px 6px #ffa299;
}

/* 列表 */
.s-list-title {
    margin-bottom: -24 rpx;
    padding-left: 30 rpx;
    padding-right: 30 rpx;
    color: #888888;
    font-size: 28 rpx;
    margin-top: 30 rpx;
}

.s-list {
    margin-top: 40 rpx;
    background-color: #fff;
    line-height: 1.47058824;
    font-size: 32 rpx;
    overflow: hidden;
    position: relative;
}

.s-list:before {
    top: 0;
    border-top: 1px solid #dcdee2;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.s-list:after,
.s-list:before {
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    color: #dcdee2;
    z-index: 2;
}

.s-list:after {
    bottom: 0;
    border-bottom: 1px solid #dcdee2;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.is-item-line {
    padding: 20 rpx 30 rpx;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    text-decoration: none;
    color: #353535;
    -webkit-tap-highlight-color: transparent;
}

.is-item-line:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid #dcdee2;
    color: #dcdee2;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    z-index: 2;
}

.is-item,
.is-item-line {
    padding: 20 rpx 30 rpx;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    text-decoration: none;
    color: #353535;
    -webkit-tap-highlight-color: transparent;
}

.is-item.has-right-icon .is-item-ft,
.is-item-line.has-right-icon .is-item-ft {
    padding-right: 26 rpx;
    position: relative;
}

.is-item.has-right-icon .is-item-ft:after,
.is-item-line.has-right-icon .is-item-ft:after {
    content: " ";
    display: inline-block;
    height: 12 rpx;
    width: 12 rpx;
    border-width: 4 rpx 4 rpx 0 0;
    border-color: #c8c8cd;
    border-style: solid;
    -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: relative;
    top: -4 rpx;
    position: absolute;
    top: 50%;
    margin-top: -8 rpx;
    right: 4 rpx;
}

.is-item:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid #dcdee2;
    color: #dcdee2;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    left: 30 rpx;
    z-index: 2;
}

.is-item:first-child:before,
.is-item-line:first-child:before {
    display: none !important;
}

.is-item-bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

.is-item-ft {
    text-align: right;
    font-size: 28 rpx;
    color: #888888;
}

.is-bd-subline {
    font-size: 28 rpx;
    color: #888888;
}

.s-list2-title {
    padding-left: 30 rpx;
    padding-right: 30 rpx;
    color: #888888;
    font-size: 28 rpx;
    margin-top: 30 rpx;
    margin-bottom: 18 rpx;
}

.s-list2 {
    background-color: #fff;
    width: 100%;
}

.is-item2 {
    position: relative;
    padding-left: 24 rpx;
    display: flex;
}

.is-list2-info {
    flex: 1;
    padding-top: 16 rpx;
    padding-bottom: 16 rpx;
    display: flex;
    flex-direction: column;
    padding-right: 24 rpx;
}

.is-list2-link .has-list2-tip {
    padding-right: 50 rpx;
}

.is-list2-img {
    margin: 16 rpx 24 rpx 16 rpx 0 rpx;
}

.is-item2.is-list2-link:after {
    content: " ";
    display: inline-block;
    height: 16 rpx;
    width: 16 rpx;
    border-width: 4 rpx 4 rpx 0 0;
    border-color: #c8c8cd;
    border-style: solid;
    -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: relative;
    top: -4 rpx;
    position: absolute;
    top: 50%;
    margin-top: -14 rpx;
    right: 24 rpx;
}

.s-list2 .is-item2:first-child .has-bordert:before {
    border: none;
}

.has-list2-tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.is-list2-tip {
    color: #888888;
    font-size: 28 rpx;
}


.s-divide {
    height: 1px;
    text-align: center;
}

.s-divide .is-divide-otext {
    position: relative;
    top: -24 rpx;
    padding: 0 40 rpx;
}

/* 顶部 底部菜单 */

.flex-sub {
    flex: 1;
}

.simple-bar {
    display: flex;
    position: relative;
    align-items: center;
    background: #fff;
    height: 100 rpx;
    justify-content: space-between;
}

.simple-bar .action {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    max-width: 100%;
}

.simple-bar .action:first-child {
    margin-left: 30 rpx;
    font-size: 30 rpx;
}

.simple-bar .action text.text-cut {
    text-align: left;
    width: 100%;
}

.simple-bar .avatar:first-child {
    margin-left: 20 rpx;
}

.simple-bar .action:first-child > text[class*='icon'] {
    margin-left: -0.3em;
    margin-right: 0.3em;
}

.simple-bar .action:last-child {
    margin-right: 30 rpx;
}

.simple-bar .action > text[class*='icon'] {
    font-size: 36 rpx;
}

.simple-bar .action > text[class*='icon'] + text[class*='icon'] {
    margin-left: 0.5em;
}

.simple-bar .content {
    position: absolute;
    text-align: center;
    width: 400 rpx;
    left: 0;
    right: 0;
    bottom: 16 rpx;
    margin: auto;
    height: 60 rpx;
    font-size: 36 rpx;
    line-height: 60 rpx;
    cursor: none;
    pointer-events: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.simple-bar.btn-group {
    justify-content: space-around;
}

.simple-bar.btn-group button {
    padding: 20 rpx 32 rpx;
}

.simple-bar.btn-group button {
    flex: 1;
    margin: 0 20 rpx;
    max-width: 50%;
}

.modal-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1110;
    opacity: 0;
    outline: 0;
    text-align: center;
    -ms-transform: scale(1.185);
    transform: scale(1.185);
    backface-visibility: hidden;
    perspective: 2000 rpx;
    background: rgba(0, 0, 0, 0.6);
    transition: all 0.6s ease-in-out 0;
    pointer-events: none;
}

.modal-box::before {
    content: '\200B';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.modal-box.show {
    opacity: 1;
    transition-duration: 0.3s;
    -ms-transform: scale(1);
    transform: scale(1);
    overflow-x: hidden;
    overflow-y: auto;
    pointer-events: auto;
}

.dialog {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin-left: auto;
    margin-right: auto;
    width: 680 rpx;
    max-width: 100%;
    background: #ffffff;
    border-radius: 16rpx;
}

.modal-box.bottom-modal::before {
    vertical-align: bottom;
}

.modal-box.bottom-modal .dialog {
    width: 100%;
    border-radius: 0;
}

.modal-box.bottom-modal {
    margin-bottom: -1000 rpx;
}

.modal-box.bottom-modal.show {
    margin-bottom: 0;
}

.bg-img {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.shadow-blur {
    position: relative;
}

.shadow-blur::before {
    content: "";
    display: block;
    background: inherit;
    filter: blur(10 rpx);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 10 rpx;
    left: 10 rpx;
    z-index: -1;
    opacity: 0.4;
    transform-origin: 0 0;
    border-radius: inherit;
    transform: scale(1, 1);
}


swiper.square-dot .wx-swiper-dot {
    background: #fff;
    opacity: 0.4;
    width: 10 rpx !important;
    height: 10 rpx !important;
    border-radius: 20 rpx !important;
    transition: all 0.3s ease-in-out 0s !important;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active {
    opacity: 1;
    width: 30 rpx !important;
}

swiper.round-dot .wx-swiper-dot {
    /* background: #39b54a; */
    width: 10 rpx !important;
    height: 10 rpx !important;
    top: -4 rpx !important;
    transition: all 0.3s ease-in-out 0s !important;
    position: relative;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after {
    content: "";
    position: absolute;
    width: 10 rpx;
    height: 10 rpx;
    top: 0 rpx;
    left: 0 rpx;
    right: 0;
    bottom: 0;
    margin: auto;
    background: #fff;
    border-radius: 20 rpx;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active {
    width: 18 rpx !important;
    height: 18 rpx !important;
    top: 0 rpx !important;
}

.screen-swiper {
    min-height: 375 rpx;
}

.screen-swiper image {
    width: 100%;
    display: block;
    height: 100%;
    margin: 0;
}

.simple-card-swiper {
    height: 420 rpx;
}

.simple-card-swiper swiper-item {
    width: 610 rpx !important;
    left: 70 rpx !important;
    box-sizing: border-box;
    padding: 40 rpx 0 rpx 70 rpx;
    overflow: initial !important;
}

.simple-card-swiper swiper-item .bg-img {
    width: 100%;
    display: block;
    height: 100%;
    border-radius: 10 rpx;
    transform: scale(0.9);
    transition: all 0.2s ease-in 0s;
}

.simple-card-swiper swiper-item.cur .bg-img {
    transform: none;
    transition: all 0.2s ease-in 0s;
}

.tower-swiper {
    height: 420 rpx;
    position: relative;
}

.tower-swiper .tower-item {
    position: absolute;
    width: 300 rpx;
    height: 380 rpx;
    top: 0;
    bottom: 0;
    left: 50%;
    margin: auto;
    transition: all 0.3s ease-in 0s;
    opacity: 1;
}

.tower-swiper .tower-item.none {
    opacity: 0;
}

.tower-swiper .tower-item .bg-img {
    width: 100%;
    height: 100%;
    border-radius: 6 rpx;
}


.simple-load {
    display: block;
    line-height: 3em;
    text-align: center;
}

.simple-load::before {
    font-family: "simplepro" !important;
    display: inline-block;
    margin-right: 6 rpx;
}

.simple-load.loading::before {
    content: "\e67a";
    animation: icon-spin 2s infinite linear;
}

.simple-load.loading::after {
    content: "加载中...";
}

.simple-load.over::before {
    content: "\e64a";
}

.simple-load.over::after {
    content: "没有更多了";
}

.simple-load.erro::before {
    content: "\e658";
}

.simple-load.erro::after {
    content: "加载失败";
}

.simple-load.load-icon::before {
    font-size: 32 rpx;
}

.simple-load.load-icon::after {
    display: none;
}

.simple-load.load-icon.over {
    display: none;
}

.simple-load.load-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 140 rpx;
    left: 0;
    margin: auto;
    width: 260 rpx;
    height: 260 rpx;
    background: #fff;
    border-radius: 10 rpx;
    box-shadow: 0 0 0 rpx 2000 rpx rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    font-size: 28 rpx;
    z-index: 999999;
    line-height: 2.4em;

}

.simple-load.load-modal [class*="icon"] {
    font-size: 60 rpx;
}

.simple-load.load-modal image {
    width: 70 rpx;
    height: 70 rpx;
}

.simple-load.load-modal::after {
    content: "";
    position: absolute;
    background: #fff;
    border-radius: 50%;
    width: 200 rpx;
    height: 200 rpx;
    font-size: 10px;
    border-top: 6 rpx solid rgba(0, 0, 0, 0.05);
    border-right: 6 rpx solid rgba(0, 0, 0, 0.05);
    border-bottom: 6 rpx solid rgba(0, 0, 0, 0.05);
    border-left: 6 rpx solid #f37b1d;
    animation: icon-spin 1s infinite linear;
    z-index: -1;
}

.load-progress {
    pointer-events: none;
    top: 0;
    position: fixed;
    width: 100%;
    left: 0;
    z-index: 2000;
}

.load-progress.hide {
    display: none;
}

.load-progress .load-progress-bar {
    position: relative;
    width: 100%;
    height: 4 rpx;
    overflow: hidden;
    transition: all 200ms ease 0s;
}


.load-progress .load-progress-spinner {
    position: absolute;
    top: 10 rpx;
    right: 10 rpx;
    z-index: 2000;
    display: block;
}

.load-progress .load-progress-spinner::after {
    content: "";
    display: block;
    width: 24 rpx;
    height: 24 rpx;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: solid 4 rpx transparent;
    border-top-color: inherit;
    border-left-color: inherit;
    border-radius: 50%;
    -webkit-animation: load-progress-spinner 0.4s linear infinite;
    animation: load-progress-spinner 0.4s linear infinite;
}

@-webkit-keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}


.cu-list.menu {
    display: block;
    overflow: hidden;
}

.cu-list + .cu-list {
    margin-top: 30 rpx;
}

.cu-list.menu > .cu-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 100 rpx;
    background: #fff;
    padding: 0 30 rpx;
}


.cu-list.menu > .cu-item::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border-bottom: 1 rpx solid #ddd;
}

.cu-list.menu.sm-border > .cu-item::after {
    width: calc(200% - 120rpx);
    left: 30 rpx;
}

.cu-list.menu > .cu-item:last-child::after {
    border: none;
}

.cu-list.menu > .cu-item.cur {
    background-color: #fcf7e9;
}

.cu-list.menu-avatar > .cu-item {
    padding-left: 140 rpx;
}

.cu-list.menu-avatar > .cu-item .cu-avatar {
    left: 30 rpx;
}

.cu-list.menu > .cu-item .content {
    line-height: 1.6em;
    flex: 1;
    font-size: 30 rpx;
}

.cu-list.menu > .cu-item button.content {
    padding: 0;
    justify-content: flex-start;
    background-color: transparent;
}

.cu-list.menu > .cu-item button.content::after {
    display: none;
}

.cu-list.menu > .cu-item .content > text[class*="icon"] {
    margin-right: 10 rpx;
    display: inline-block;
    width: 1.6em;
    text-align: center;
}

.cu-list.menu > .cu-item .content > image {
    margin-right: 10 rpx;
    display: inline-block;
    width: 1.6em;
    height: 1.6em;
    vertical-align: middle;
}

.cu-list.menu-avatar > .cu-item .action {
    text-align: center;
}

.cu-list.menu-avatar > .cu-item .action view + view {
    margin-top: 10 rpx;
}

.cu-list.menu > .cu-item .action .cu-tag:empty {
    right: 10 rpx;
}

.cu-list.menu > .cu-item.arrow {
    padding-right: 90 rpx;
}

.cu-list.menu > .cu-item.arrow::before {
    font-family: "simplepro" !important;
    display: block;
    content: "\e6a3";
    position: absolute;
    font-size: 34 rpx;
    color: #aaa;
    line-height: 30 rpx;
    height: 30 rpx;
    width: 30 rpx;
    text-align: center;
    top: 0 rpx;
    bottom: 0;
    right: 30 rpx;
    margin: auto;
}

.cu-list.menu > .cu-item .cu-avatar-group .cu-avatar {
    border-color: #fff;
}

.cu-list.card-menu {
    margin-left: 30 rpx;
    margin-right: 30 rpx;
    border-radius: 20 rpx;
    overflow: hidden;
}

.cu-list.menu-avatar > .cu-item {
    padding-left: 140 rpx;
    height: 140 rpx;
}

.cu-list.menu-avatar > .cu-item > .cu-avatar {
    position: absolute;
    left: 30 rpx;
}

.cu-list.menu-avatar.comment > .cu-item {
    height: auto;
    padding-top: 30 rpx;
    padding-bottom: 30 rpx;
    padding-left: 120 rpx;
}

.cu-list.menu-avatar.comment .cu-avatar {
    align-self: flex-start;
}

.cu-list.menu > .cu-item .content .cu-tag.sm {
    font-size: 16 rpx;
    line-height: 80%;
    padding: 8 rpx 6 rpx 4 rpx;
    margin-top: -6 rpx;
}

.cu-list.grid {
    text-align: center;
    background: #fff;
}

.cu-list.grid > .cu-item {
    display: flex;
    flex-direction: column;
    padding: 20 rpx 0 30 rpx;
    position: relative;
    transition-duration: 0s;
}

.cu-list.grid > .cu-item [class*="icon"] {
    display: block;
    width: 100%;
    position: relative;
    font-size: 48 rpx;
    margin-top: 20 rpx;
}

.cu-list.grid > .cu-item text {
    display: block;
    color: #888;
    margin-top: 10 rpx;
    line-height: 40 rpx;
    font-size: 26 rpx;
}

.cu-list.grid > .cu-item .cu-tag {
    left: 50%;
    right: auto;
    margin-left: 20 rpx;
}

.cu-list.grid > .cu-item::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.cu-list.grid.col-3 > .cu-item:nth-child(3n)::after {
    border-right-width: 0px;
}

.cu-list.grid.col-4 > .cu-item:nth-child(4n)::after {
    border-right-width: 0px;
}

.cu-list.grid.col-5 > .cu-item:nth-child(5n)::after {
    border-right-width: 0px;
}

.cu-list.grid.no-border {
    padding: 20 rpx 10 rpx;
}

.cu-list.grid.no-border > .cu-item {
    padding-top: 10 rpx;
    padding-bottom: 20 rpx;
}

.cu-list.grid.no-border > .cu-item::after {
    border: none !important;
}

.cu-list > .cu-item {
    transform: translateX(0 rpx);
    transition: all 0.6s ease-in-out 0s;
}

.cu-list > .cu-item .move {
    display: flex;
    width: 260 rpx;
    height: 100%;
    position: absolute;
    right: 0;
    transform: translateX(100%);
}

.cu-list > .cu-item.move-cur {
    transform: translateX(-260 rpx);
}

.cu-list > .cu-item .move view {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}
