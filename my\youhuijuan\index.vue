<template>
	<view style="padding-bottom: 100rpx;">
		<view style="margin: 30rpx 30rpx;height: 370rpx;">
			<view class="wdq_view">
				<view class="wdq_toptext">优惠劵商城，您的省钱小帮手</view>
			</view>
			<view class="wdq_my">
				<view class="flex justify-between align-center">
					<view class="wdq_mytext">我的优惠券</view>
					<view class="flex align-center">
						<view class="wdq_mychakan" @tap="goMyQuan">查看更多</view>
						<image src="../../static/images/my/right.png"></image>
					</view>
				</view>
				<view class="margin-top" v-if="yhqList && yhqList.length > 0">
					<scroll-view class="scroll-view_H" scroll-x="true" scroll-left="0">
						<view class="flex">
							<view class="wdq_itemview" v-for="(item,index) in yhqList">
								<image src="../static/yhqbackgroud.png" class="wdq_itemimage"></image>
								<view class="itemview_item">
									<view class="item_moneyview">￥<text>{{item.money}}</text> </view>
									<view class="item_rightview">
										<view>
											<view class="item_coupName">{{item.couponName}}</view>
											<view class="item_coupEndTime" v-if="item.expirationTime">
												到期：{{item.expirationTime}}</view>
											<view class="item_coupEndTime" v-else> 永久有效</view>
										</view>
										<view class="item_coupBtn" @click="shiyong()">去使用</view>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<view style="width: 100%;display: flex;justify-content: center;" v-else>
					<image src="../static/noyhq.png" style="width: 251rpx;height: 178rpx;"></image>
				</view>
			</view>
		</view>

		<view class="hd_yhqview">
			<view class="hd_topview">活动优惠券</view>

			<view class="margin-top" v-if="hdyhqList && hdyhqList.length > 0">
				<scroll-view class="hdscroll-view_H" scroll-x="true" scroll-left="0">
					<view class="flex">
						<view class="hdwdq_itemview" v-for="(item,index) in hdyhqList">
							<image src="../static/hdyhq1.png" class="hdwdq_itemimage"></image>
							<view class="hditemview_item">
								<view class="hd_itemname">{{item.couponName}}</view>
								<view class="hditem_moneyview">￥<text>{{item.money}}</text> </view>
								<view class="hd_itemtext" v-if="item.minMoney">满{{item.minMoney}}元使用</view>
								<view class="hd_itemtext" v-else>无门槛使用</view>
								<view class="hd_itembtn" @click="lingqu(item)">去领取</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			<view style="width: 100%;display: flex;justify-content: center;margin-top: 40rpx;" v-else>
				<image src="../static/noyhq1.png" style="width: 251rpx;height: 178rpx;"></image>
			</view>
		</view>

		<!-- <view class="qb_view">
			<view class="hd_topview">超值券包 <text>购券下单更优惠</text> </view>

			<scroll-view class="qbscroll-view_H" scroll-y="true" scroll-left="0"
				v-if="qbyhqList && qbyhqList.length > 0">
				<view class="qb_itemview" v-for="(item,index) in qbyhqList">
					<image src="../static/wmkquan.png"></image>
					<view style="margin-left: 20rpx;width: 220rpx;">
						<view class="qb_itemname">{{item.couponName}}</view>
						<view class="flex align-end margin-top-xs">
							<view class="qb_money">￥ <text style="font-size: 34rpx;">{{item.buyMoney}}</text> </view>
						</view>
					</view>
					<view class="qb_btn" @tap="showPay(item.couponId)">立即抢购</view>
				</view>
			</scroll-view>

			<view style="width: 100%;display: flex;justify-content: center;margin-top: 240rpx;" v-else>
				<image src="../static/noyhq1.png" style="width: 251rpx;height: 178rpx;"></image>
			</view>
		</view>
 -->
		<!-- 支付方式 -->
		<u-popup v-model="showpays" mode="bottom" :closeable="closeable">
			<view class="popup_pay">
				<view style="background-color: #fff;">
					<view style="padding: 0 20upx;margin-top: 60rpx;margin-bottom: 20rpx;">
						<view
							style="display: flex;height: 100upx;align-items: center;padding: 20upx 0;justify-content: center;"
							v-for="(item,index) in openLists" :key='index'>
							<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;">
							</image>
							<view style="font-size: 30upx;margin-left: 20upx;width: 70%;">
								{{item.text}}
							</view>
							<radio-group name="openWay" style="margin-left: 45upx;" @tap='selectWay(item)'>
								<label class="tui-radio">
									<radio color="#1777FF" :checked="openWay === item.id ? true : false" />
								</label>
							</radio-group>
						</view>
					</view>
				</view>
				<view class="pay_btn" @click="payYHQ()">确认支付</view>
			</view>
		</u-popup>

		<!-- <u-popup v-model="showkm" @close="closekm" mode="center" borderRadius="32">
			<view class="popu_view">
				<view class="popu_viewtitle">卡密兑换</view>
				<view class="popu_kamiview">
					<view class="kamiview_lefttext">卡密：</view>
					<input type="text" class="kamiview_righttext" v-model="kamiText" placeholder="请输入卡密信息"
						placeholder-class="placeholderclass" />
				</view>
				<view class="kami_btn" @tap="addKaMi">立即兑换</view>
			</view>
		</u-popup>
 -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				couponId: 0,
				showpays: false,
				closeable: true,
				openLists: [],
				openWay: 2,
				yhqList: [],
				hdyhqList: [],
				qbyhqList: []
			}
		},
		onLoad() {
			// #ifdef APP-PLUS
			this.openLists = [{
				image: '../../static/images/zhifubao.png',
				text: '支付宝',
				id: 2
			}, {
				image: '../../static/images/icon_weixin.png',
				text: '微信',
				id: 1
			}, {
				image: '../../static/images/lingqian.png',
				text: '水贝',
				id: 3
			}];
			this.openWay = 1;
			// #endif

			// #ifdef MP-WEIXIN
			this.openLists = [{
				image: '../../static/images/icon_weixin.png',
				text: '微信',
				id: 1
			}, {
				image: '../../static/images/lingqian.png',
				text: '水贝',
				id: 3
			}];
			this.openWay = 1;
			// #endif

			// #ifdef H5
			this.openLists = [{
				image: '../../static/images/zhifubao.png',
				text: '支付宝',
				id: 2
			}, {
				image: '../../static/images/lingqian.png',
				text: '水贝',
				id: 3
			}];
			this.openWay = 2;
			// #endif
			this.getHuoDong();
			this.getMyList();
		},
		methods: {
			lingqu(e) {
				this.$Request.getT('/app/couponUser/receiveActivity?couponId=' + e.couponId).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '领取成功',
							icon: 'none'
						})
						this.getMyList()
					} else {
						this.$queue.showToast(res.msg);
					}
				});
			},
			shiyong() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			getHuoDong() {

				this.$Request.getT('/app/coupon/getCouponPageList?page=1&limit=50&couponType=3').then(res => {
					if (res.code == 0) {
						this.hdyhqList = res.data.records;
					}
				});

				// this.$Request.getT('/app/coupon/getCouponPageList?page=1&limit=50&couponType=2').then(res => {
				// 	if (res.code == 0) {
				// 		this.qbyhqList = res.data.records;
				// 	}
				// });
			},
			getMyList() {
				this.$Request.getT('/app/couponUser/getMyCouponList?page=1&limit=10&status=0').then(res => {
					if (res.code == 0) {
						let data = res.data.records
						for (var i = 0; i < data.length; i++) {
							if (data[i].expirationTime) {
								data[i].expirationTime = data[i].expirationTime.substring(0, 10)
							}
						}
						this.yhqList = res.data.records;

					}
				});
			},
			goMyQuan() {
				uni.navigateTo({
					url: '/my/youhuijuan/myList'
				});
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			showPay(couponId) {
				console.log(couponId)
				this.couponId = couponId;
				this.showpays = true;
			},
			payYHQ() {
				let that = this;
				that.$queue.showLoading('支付中...');
				if (that.openWay == 1) {
					// #ifdef MP-WEIXIN
					that.$Request.postT('/app/wxPay/payCoupon?payType=3&couponId=' + that.couponId + '&buyNum=1').then(
						ret => {
							that.showpays = false;
							if (ret.code == 0) {
								uni.requestPayment({
									provider: 'wxpay',
									timeStamp: ret.data.timestamp,
									nonceStr: ret.data.noncestr,
									package: ret.data.package,
									signType: ret.data.signType,
									paySign: ret.data.sign,
									success: function(suc) {
										console.log('success:' + JSON.stringify(suc));
										uni.showToast({
											title: '支付成功',
											icon: 'success'
										})
										setTimeout(d => {
											that.getMyList();
											that.getHuoDong();
										}, 1000);
									},
									fail: function(err) {
										console.log('fail:' + JSON.stringify(err));
										uni.showToast({
											title: '支付失败',
											icon: 'none'
										})
									}
								});
							} else {
								that.$queue.showToast(res.msg);
							}
						});
					// #endif
					// #ifdef H5
					that.$Request.post("/app/wxPay/payCoupon?payType=2&couponId=" + that.couponId + '&buyNum=1').then(
						red => {
							that.showpays = false;
							if (red.code == 0) {
								that.callPay(red);
							} else {
								that.$queue.showToast(red.msg);
							}
						});
					// #endif

					// #ifdef APP-PLUS
					that.$Request.post("/app/wxPay/payCoupon?payType=1&couponId=" + that.couponId + '&buyNum=1')
						.then(red => {
							that.showpays = false;
							if (red.code == 0) {
								that.setPayment('wxpay', JSON.stringify(red
									.data));
							} else {
								that.$queue.showToast(red.msg);
							}
						});
					// #endif
				} else if (that.openWay == 2) {
					// #ifdef H5
					that.$Request.post("/app/aliPay/payCoupon?payType=2&couponId=" + that.couponId + '&buyNum=1').then(
						red => {
							that.showpays = false;
							if (red.code == 0) {
								const div = document.createElement('div')
								div.innerHTML = red.data //此处form就是后台返回接收到的数据
								document.body.appendChild(div)
								document.forms[0].submit()
							} else {
								uni.showToast({
									title: red.msg,
									icon: 'none'
								})
							}
						});
					// #endif
					// #ifdef APP-PLUS
					that.$Request.post("/app/aliPay/payCoupon?payType=1&couponId=" + that.couponId + '&buyNum=1').then(
						red => {
							that.showpays = false;
							if (red.code == 0) {
								that.setPayment('alipay', red.data);
							} else {
								uni.showToast({
									title: red.msg,
									icon: 'none'
								})
							}
						});
					// #endif
				} else if (that.openWay == 3) {
					that.$Request.postT('/app/couponUser/buyCoupon?couponId=' + that.couponId + '&buyNum=1').then(res => {
						uni.hideLoading();
						that.showpays = false;
						if (res.code == 0) {
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							})
							setTimeout(d => {
								that.getMyList();
								that.getHuoDong();
							}, 1000);
						} else {
							that.$queue.showToast(res.msg);
						}
					});
				}
			},
			callPay: function(response) {
				if (typeof WeixinJSBridge === "undefined") {
					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.hideLoading();
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							})
							setTimeout(d => {
								that.getMyList();
								that.getHuoDong();
							}, 1000);
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			setPayment(name, order) {
				let that = this;
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						uni.hideLoading();
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						})
						setTimeout(d => {
							that.getMyList();
							that.getHuoDong();
						}, 1000);
					},
					fail: function(err) {
						uni.hideLoading();
						console.log(12)
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #F2F2F2;
	}

	.popu_view {
		width: 690rpx;
		height: 420rpx;
		padding: 40rpx 30rpx;

		.popu_viewtitle {
			width: 100%;
			text-align: center;
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
		}

		.kami_btn {
			width: 640rpx;
			height: 88rpx;
			background: #268CFF;
			border-radius: 44rpx;
			text-align: center;
			line-height: 88rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			margin-top: 40rpx;
		}

		.popu_kamiview {
			display: flex;
			align-items: center;
			width: 630rpx;
			height: 100rpx;
			background: #F7F5F5;
			border-radius: 10rpx;
			margin-top: 60rpx;
			padding: 0rpx 20rpx;

			.kamiview_lefttext {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
			}

			.kamiview_righttext {
				width: 480rpx;
				height: 100rpx;
				font-size: 30rpx;
				color: #333333;
			}

		}

		.placeholderclass {
			font-size: 30rpx;
			color: #333333;
		}
	}


	.qb_view {
		width: 686rpx;
		height: 800rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 30rpx;

		.qbscroll-view_H {
			width: 100%;
			height: 700rpx;
			padding: 30rpx 0rpx;
		}

		.qb_itemview {
			width: 626rpx;
			height: 160rpx;
			background: #FFFFFF;
			border: 1rpx solid #FEE3C0;
			border-radius: 24rpx;
			padding: 20rpx;
			margin-top: 20rpx;
			display: flex;
			align-items: center;

			.qb_btn {
				width: 140rpx;
				height: 60rpx;
				background: linear-gradient(90deg, #ED724B 0%, #FD4136 100%);
				border-radius: 30rpx;
				text-align: center;
				line-height: 60rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				margin-left: 20rpx;
			}

			.qb_itemname {
				width: 200rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #1A1A1A;
			}

			.qb_money {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #F01B1F;
			}

			.qb_oldmoney {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				// text-decoration: line-through;
				color: #999999;
				margin-left: 10rpx;
			}

			image {
				width: 195rpx;
				height: 96rpx;
			}
		}
	}

	.hd_topview {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #050505;

		text {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-left: 20rpx;
		}
	}

	.wdq_view {
		width: 686rpx;
		height: 130rpx;
		background: linear-gradient(90deg, #31A1FE 0%, #2589FF 100%);
		border-radius: 24rpx;
	}

	.wdq_itemview {
		margin-right: 20rpx;
		width: 560rpx;
		height: 140rpx;

		.wdq_itemimage {
			width: 560rpx;
			height: 140rpx;
		}

		.itemview_item {
			display: flex;
			position: absolute;
			top: 0rpx;
			width: 560rpx;
			height: 140rpx;
			align-items: center;
			z-index: 99;

			.item_moneyview {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #84592B;
				width: 140rpx;
				text-align: center;

				text {
					font-size: 48rpx;
				}
			}

			.item_rightview {
				margin-left: 40rpx;
				display: flex;
				justify-content: space-between;
				width: 360rpx;
				height: 140rpx;
				align-items: center;

				.item_coupName {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 800;
					color: #1A1A1A;
					width: 200rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.item_coupEndTime {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #999999;
					margin-top: 16rpx;
				}

				.item_coupBtn {
					width: 130rpx;
					height: 50rpx;
					background: linear-gradient(90deg, #ED724B 0%, #FD4136 100%);
					border-radius: 25rpx;
					text-align: center;
					line-height: 50rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
				}
			}
		}
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
		height: 150rpx;
		display: flex;
	}

	.wdq_my {
		width: 686rpx;
		height: 280rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		position: absolute;
		/* #ifdef H5 */
		top: 90rpx;
		/* #endif */
		/* #ifndef H5 */
		top: 120rpx;
		/* #endif */
		z-index: 99;
		padding: 30rpx;

		.wdq_mytext {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 800;
			color: #050505;
		}

		.wdq_mychakan {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-right: 10rpx;
		}

		image {
			width: 11rpx;
			height: 20rpx;
		}
	}

	.popup_pay {
		width: 100%;
		position: relative;
		padding-bottom: 45rpx;
	}

	.pay_btn {
		width: 90%;
		margin: 0 auto;
		text-align: center;
		background: #0278FE;
		height: 80rpx;
		border-radius: 16rpx;
		color: #ffffff;
		line-height: 80rpx;

	}

	.wdq_toptext {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		padding: 30rpx;
	}

	.imag_dw {
		position: absolute;
		top: 18upx;
		left: 15upx;
		z-index: 99;
		color: #ED724B;
		font-size: 32upx;
		font-weight: bold;
		letter-spacing: 1rpx;
	}

	.imag_dw text {
		font-size: 24upx;
	}

	.hd_yhqview {
		width: 686rpx;
		height: 380rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 30rpx;

		.hdscroll-view_H {
			white-space: nowrap;
			width: 100%;
			height: 280rpx;
			display: flex;
		}

		.hdwdq_itemview {
			margin-right: 20rpx;
			width: 220rpx;
			height: 260rpx;

			.hdwdq_itemimage {
				width: 220rpx;
				height: 260rpx;
			}

			.hditemview_item {
				position: absolute;
				top: 0rpx;
				width: 220rpx;
				height: 260rpx;
				z-index: 99;
				text-align: center;
				padding: 20rpx 0rpx;
				justify-content: center;

				.hd_itemname {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #1A1A1A;
				}

				.hd_itemtext {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #999999;
				}

				.hd_itembtn {
					width: 130rpx;
					height: 50rpx;
					background: #FEFAEF;
					border-radius: 25rpx;
					margin-top: 40rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #F90C14;
					text-align: center;
					line-height: 50rpx;
					margin-left: 44rpx;
				}

				.hditem_moneyview {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #F01B1F;
					width: 220rpx;
					text-align: center;
					margin: 10rpx 0rpx;

					text {
						font-size: 40rpx;
					}
				}
			}
		}
	}
</style>
