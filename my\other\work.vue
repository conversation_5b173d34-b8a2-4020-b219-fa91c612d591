<template>
	<view class="content">
		<image class="bgimg" src="https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/20fbd74a515bc261473d8ac593e1a045.png" mode="widthFix"></image>

		<view class="name">
			<view class="name-box">
				<image src="../static/name.png" mode=""></image>
			</view>
		</view>
		<view class="sm">
			<view class="sm-box">
				<view class="sm-box-title">{{contentTitle}}</view>
				<view class="sm-box-sm" v-html="content"></view>
			</view>
		</view>
		<view class="lx">
			<view class="lx-box">
				<view class="lx-box-con">
					<view class="lx-box-con-item">
						<view class="lx-box-con-item-t">联系人姓名</view>
						<u-input placeholder="请输入联系人姓名" v-model="userName" :clearable="false"></u-input>
					</view>
					<view class="line"></view>
					<view class="lx-box-con-item" style="margin-top: 30rpx;">
						<view class="lx-box-con-item-t">联系人手机号</view>
						<u-input placeholder="请输入联系人手机号" v-model="phone" :clearable="false"></u-input>
					</view>
				</view>
			</view>
		</view>
		<view class="btn">
			<view class="btn-box">
				<view class="btn-box-l" @click="submit()">确认提交</view>
				<view class="btn-box-r" @click="Changekefu()">直接联系</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				userName: '',
				phone: '',
				content:'',
				contentTitle:''
				
			};
		},
		onLoad() {
			this.getwenben()
		},
		onShow() {

		},
		methods: {
			getwenben() {
			// 招聘岗位描述 315
			this.$Request.get('/app/common/type/315').then(res => {
				if (res.code == 0) {
					this.contentTitle = res.data.min
					this.content = res.data.value
				}
			});
			},
			Changekefu() {
				let SelKeFu = this.$queue.getData('SelKeFu');
				if (SelKeFu + '' == '1') { //手机号
					uni.makePhoneCall({
						phoneNumber: uni.getStorageSync('kefuPhone')
					});
				} else if (SelKeFu + '' == '2') { //企业微信
					let that = this
					// #ifdef MP-WEIXIN
					wx.openCustomerServiceChat({
						extInfo: {
							url: that.$queue.getData('kefu')
						},
						corpId: that.$queue.getData('kefuAppId'),
						success(res) {}
					})
					// #endif
					// #ifdef H5
					window.location.href = that.$queue.getData('kefu');
					// #endif
					// #ifdef APP
					let kefu = that.$queue.getData('kefu')
					console.log(kefu)
					plus.runtime.openURL(kefu, function(res) {});
					// #endif
				} else { //客服二维码页面
					uni.navigateTo({
						url: '/pages/kefu/kefu'
					})
				}
			},
			submit() {
				if (!this.userName) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					})
					return
				}
				if (!this.phone) {
					uni.showToast({
						title: '请输入联系人手机号码',
						icon: 'none'
					})
					return
				}

				let data = {
					userName: this.userName,
					phone: this.phone,
					classify: 1
				}

				this.$Request.postJson("//app/cityAgency/insertCityAgency", data).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '提交成功！',
							icon: 'none'
						})
						setTimeout(function() {
							uni.navigateBack()
						}, 1000)

					}
				})
			}
		}
	}
</script>

<style lang="less">
	.content {
		width: 100%;
		min-height: 100vh;
	}

	.bgimg {
		width: 100%;
		position: relative;
	}

	.name {
		width: 100%;
		height: auto;
		position: absolute;
		top: 530rpx;
		display: flex;
		justify-content: center;
		z-index: 999;

		.name-box {
			width: 686rpx;
			height: auto;
			display: flex;
			justify-content: center;

			image {
				width: 400rpx;
				height: 110rpx;
			}
		}
	}

	.sm {
		width: 100%;
		height: 600rpx;
		position: absolute;
		top: 615rpx;
		display: flex;
		justify-content: center;

		.sm-box {
			width: 613rpx;
			height: 100%;
			border-radius: 24rpx;
			background-color: #E8EEFF;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;

			.sm-box-title {
				width: 100%;
				margin-top: 50rpx;
				color: #2E40F7;
				font-size: 30rpx;
				font-weight: 800;
				text-align: center;
			}

			.sm-box-sm {
				width: 544rpx;
				text-align: center;
				color: #2E40F7;
				font-size: 24rpx;

			}
		}
	}

	.lx {
		width: 100%;
		// height: 321rpx;
		display: flex;
		justify-content: center;
		position: absolute;
		top: 1250rpx;

		.lx-box {
			width: 613rpx;
			height: 100%;
			background-color: #E8EEFF;
			border-radius: 24rpx;
			display: flex;
			justify-content: center;
			padding-bottom: 30upx;

			.lx-box-con {
				width: 547rpx;
				height: 100%;
			}

			.line {
				width: 100%;
				border: 1rpx solid #B4BCFF;
				margin-top: 10rpx;
			}

			.lx-box-con-item {
				width: 100%;
				margin-top: 50rpx;

				.lx-box-con-item-t {
					color: #333333;
					font-size: 30rpx;
					// font-weight: 800;
					margin-bottom: 10rpx;
				}
			}
		}
	}

	.btn {
		width: 100%;
		height: 88rpx;
		display: flex;
		justify-content: center;
		position: absolute;
		top: 1660rpx;

		.btn-box {
			width: 613rpx;
			height: 100%;
			display: flex;
			justify-content: space-between;

			.btn-box-l {
				width: 290rpx;
				height: 100%;
				border-radius: 44rpx;
				background-color: #97A8F8;
				color: #FFFFFF;
				font-size: 28rpx;
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.btn-box-r {
				width: 290rpx;
				height: 100%;
				border-radius: 44rpx;
				background-color: #435DF0;
				color: #FFFFFF;
				font-size: 28rpx;
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
</style>
