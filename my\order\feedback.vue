<template>
	<view class="page">
		<view class="feedback-title">
			<text>评价</text>
			<!-- <text @tap="chooseMsg">快速键入</text> -->
		</view>
		<view class="feedback-body"><textarea placeholder="请输入你的评价..." v-model="sendDate.content"
				class="feedback-textare" /></view>
		<!-- <view class="feedback-title"><text>QQ/邮箱</text></view> -->
		<!-- <view class="feedback-body"><input class="feedback-input" v-model="sendDate.mail" placeholder="方便我们联系你 " /></view> -->
		<view class="feedback-title feedback-star-view">
			<text>订单评分</text>
			<view class="feedback-star-view">
				<!-- <text class="feedback-star" v-for="(value, key) in stars" :key="key" :class="key < sendDate.score ? 'active' : ''" @tap="chooseStar(value)"></text> -->
			</view>
			<u-rate :count="count" v-model="value"></u-rate>
		</view>
		<view class="img flex justify-center">
			<view class="img-box">
				<view class="img-box-title">
					评价图片
				</view>
				<view class="img-box-imgs flex flex-wrap justify-between">
					<view class="img-box-imgs-s" v-for="(item,index) in imageList" :key="index">
						<image :src="item" mode=""></image>
						<u-icon @click="close(index)" name="close-circle" style="position: absolute;right: -15rpx;top: -15rpx;z-index: 99 !important;" color="red" size="38"></u-icon>
					</view>
					
					<view v-if="imageList.length<9" class="img-box-imgs-s uploud" @click="chooseImg" style="">
						<view class="" style="width: 100%;text-align: center;">
							<u-icon name="camera" color="#8f8f94" size="60"></u-icon>
						</view>
						<view class="" style="width: 100%;text-align: center;font-size: 20rpx;color: #8f8f94;">
							添加图片
						</view>
					</view>
					<view class="img-box-imgs-s" style="height: 0;"></view>
				</view>
			</view>
		</view>
		<button type="primary" style="background: #557EFD;margin-top: 32upx;" class="feedback-submit"
			@tap="send">提交</button>
	</view>
</template>

<script>
	import config from '../../common/config.js'
	export default {
		data() {
			return {
				msgContents: ['界面显示错乱', '启动缓慢，卡出翔了', 'UI无法直视，丑哭了', '偶发性崩溃'],
				stars: [1, 2, 3, 4, 5],
				imageList: [],
				sendDate: {
					score: 5,
					content: '',
					contact: ''
				},
				id: '',
				count: 5,
				value: 5,
				ordersNo: ''
			};
		},
		onLoad(e) {
			//获取订单id
			if(e.ordersNo){
				this.ordersNo = e.ordersNo
			}
		},
		methods: {
			//删除图片
			close(e) {
				this.imageList.splice(e, 1);
			},
			chooseMsg() {
				//快速输入
				uni.showActionSheet({
					itemList: this.msgContents,
					success: res => {
						this.sendDate.content = this.msgContents[res.tapIndex];
					}
				});
			},
			//上传图片
			chooseImg() {
				let that = this
				//选择图片
				uni.chooseImage({
					sourceType: ['camera', 'album'],
					sizeType: 'compressed',
					count: 1,
					success: res => {
						//循环上传图片
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: config.APIHOST1 + '/alioss/upload', //真实的接口地址
								// url: 'https://jiazheng.xianmxkj.com/sqx_fast/alioss/upload',
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									that.imageList.push(JSON.parse(uploadFileRes.data).data)
									uni.hideLoading();
								}
							});
						}
					}
				});
			},
			chooseStar(e) {
				//点击评星
				this.sendDate.score = e;
			},
			send() {
				//发送反馈
				if (!this.sendDate.content) {
					uni.showToast({
						icon: 'none',
						title: '请输入评价内容'
					});
					return;
				}
				// if (!this.sendDate.contact) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请填写QQ或邮箱'
				// 	});
				// 	return;
				// }
				// uni.report('意见反馈', this.sendDate);
				this.$queue.showLoading('加载中...');
				this.$Request.post('/app/evaluate/addEvaluate', {
					// id: this.id,
					userId:uni.getStorageSync('userId'),
					evaluateMessage: this.sendDate.content,
					satisfactionFlag: this.value,
					indentNumber: this.ordersNo,
					evaluateImg:this.imageList.join(',')
				}).then(res => {
					if (res.code === 0) {
						uni.showToast({
							title: '评价成功'
						});
						setTimeout(function() {
							uni.navigateBack();
						}, 1000);
					} else {
						uni.hideLoading();
						uni.showModal({
							showCancel: false,
							title: '评价失败',
							content: res.msg
						});
					}
				});
			}
		}
	};
</script>

<style>
	@font-face {
		font-family: uniicons;
		font-weight: normal;
		font-style: normal;
		src: url('https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf') format('truetype');
	}

	page {
		background-color: #F5F5F5;
	}

	view {
		font-size: 28upx;
	}
	.img{
		width: 100%;
		height: auto;
	}
	.img-box{
		width: 95%;
		height: 100%;
	}
	.img-box-title{
		color: #8f8f94;
	}
	.img-box-imgs{
		margin-top: 10rpx;
	}
	.img-box-imgs-s{
		width: 220rpx;
		height: 220rpx;
		/* margin-left: 10rpx; */
		margin-bottom: 20rpx;
		position: relative;
		border-radius: 10rpx;
	}
	.uploud{
		border: 2rpx dashed #8f8f94;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 60rpx;
		flex-wrap: wrap;
		align-content: center;
	}
	.img-box-imgs-s>image{
		width: 100%;
		height: 100%;
		z-index: 1;
		border-radius: 10rpx;
	}
	
	.input-view {
		font-size: 28upx;
	}

	.close-view {
		text-align: center;
		line-height: 14px;
		height: 16px;
		width: 16px;
		border-radius: 50%;
		background: #ff5053;
		color: #ffffff;
		position: absolute;
		top: -6px;
		right: -4px;
		font-size: 12px;
	}

	/* 上传 */
	.uni-uploader {
		flex: 1;
		flex-direction: column;
	}

	.uni-uploader-head {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.uni-uploader-info {
		color: #b2b2b2;
	}

	.uni-uploader-body {
		margin-top: 16upx;
	}

	.uni-uploader__files {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.uni-uploader__file {
		margin: 10upx;
		width: 210upx;
		height: 210upx;
	}

	.uni-uploader__img {
		display: block;
		width: 210upx;
		height: 210upx;
	}

	.uni-uploader__input-box {
		position: relative;
		margin: 10upx;
		width: 208upx;
		height: 208upx;
		border: 2upx solid #d9d9d9;
	}

	.uni-uploader__input-box:before,
	.uni-uploader__input-box:after {
		content: ' ';
		position: absolute;
		top: 50%;
		left: 50%;
		-webkit-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
		background-color: #d9d9d9;
	}

	.uni-uploader__input-box:before {
		width: 4upx;
		height: 79upx;
	}

	.uni-uploader__input-box:after {
		width: 79upx;
		height: 4upx;
	}

	.uni-uploader__input-box:active {
		border-color: #999999;
	}

	.uni-uploader__input-box:active:before,
	.uni-uploader__input-box:active:after {
		background-color: #999999;
	}

	.uni-uploader__input {
		position: absolute;
		z-index: 1;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
	}

	/*问题反馈*/
	.feedback-title {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 20upx;
		color: #8f8f94;
		font-size: 28upx;
	}

	.feedback-star-view.feedback-title {
		justify-content: flex-start;
		margin: 0;
	}

	.feedback-quick {
		position: relative;
		padding-right: 40upx;
	}

	.feedback-quick:after {
		font-family: uniicons;
		font-size: 40upx;
		content: '\e581';
		position: absolute;
		right: 0;
		top: 50%;
		color: #bbb;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
	}

	.feedback-body {
		font-size: 32upx;
		padding: 16upx;
		margin: 16upx;
		border-radius: 16upx;
		background: #FFFFFF;
		/* color: #FFF; */
	}

	.feedback-textare {
		height: 200upx;
		font-size: 34upx;
		line-height: 50upx;
		width: 100%;
		box-sizing: border-box;
		padding: 20upx 30upx 0;
		color: #8f8f94;
	}

	.feedback-input {
		font-size: 32upx;
		height: 60upx;
		padding: 15upx 20upx;
		line-height: 60upx;
	}

	.feedback-uploader {
		padding: 22upx 20upx;
	}

	.feedback-star {
		font-family: uniicons;
		font-size: 40upx;
		margin-left: 6upx;
	}

	.feedback-star-view {
		margin-left: 20upx;
	}

	.feedback-star:after {
		content: '\e408';
	}

	.feedback-star.active {
		color: #ffb400;
	}

	.feedback-star.active:after {
		content: '\e438';
	}

	.feedback-submit {
		background: #007aff;
		color: #ffffff;
		margin: 20upx;
	}
</style>
