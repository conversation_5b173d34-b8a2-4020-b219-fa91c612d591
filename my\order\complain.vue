<template>
	<view class="page" style="background-color: #ffffff;">
		<view class="feedback-title">投诉标题</view>
		<view class="feedback-body">
			{{title}}<!-- <input class="feedback-input" v-model="sendDate.title" placeholder="请输入投诉标题 " /> -->
		</view>
		<view class="feedback-title">投诉内容</view>
		<view class="feedback-body" style="height: 300upx;">
			<u-input v-model="sendDate.content" type="textarea" placeholder="请详细描述你的问题... " height="300"/>
			<!-- <input class="feedback-input" type="textarea" v-model="sendDate.content" placeholder="请详细描述你的问题... "  /> -->
			<!-- <textarea placeholder="请详细描述你的问题..." style="" v-model="sendDate.content" class="feedback-textare" /> -->
		</view>
		<!-- <view class="feedback-title">QQ/邮箱</view>
		<view class="feedback-body">
			<input class="feedback-input" v-model="sendDate.contact" placeholder="方便我们联系你 " />
		</view> -->
		<view class="feedback-title">投诉图</view>
		<view class="margin-lr-sm">
			<view class="flex " style="overflow: hidden;flex-direction: initial;flex-wrap: wrap;">
				<view v-if="Imgo.length">
					<view class=" flex margin-right-sm flex-wrap">
						<view class="flex margin-top-xs"
							style="width: 200rpx;height: 200rpx;margin-right: 5rpx;position: relative;"
							v-for="(image,index) in Imgo" :key="index">
							<image :src="image" style="width: 100%;height: 100%;"></image>
							<view style="z-index: 9;position: absolute;top: -15rpx;right: -15rpx;"
								@click="removeImg(index)">
								<u-icon name="close-circle-fill" color="#2979ff" size="50rpx"></u-icon>
							</view>

						</view>
					</view>
				</view>
				<view class="" @click="addImages(2)" v-if="Imgo.length<=5">
					<view style="width: 200rpx;height: 200rpx;background: #f4f5f6;"
						class="flex justify-center align-center">
						<view>
							<view class="text-center">
								<u-icon name="plus" color="#666666" size="28"></u-icon>
							</view>
							<view class="text-center">选择图片</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<button type="primary" style="" class="feedback-submit" @tap="send">提交</button>
	</view>
</template>

<script>
	import configdata from '@/common/config.js';
	export default {
		data() {
			return {
				msgContents: ['界面显示错乱', '启动缓慢，卡出翔了', 'UI无法直视，丑哭了', '偶发性崩溃'],
				stars: [1, 2, 3, 4, 5],
				imageList: [],
				sendDate: {
					content: '',
					// contact: '',
					title: '',
					image: "",
				},
				ordersId: '',
				Imgo: [],
				byUserId: '',
				byuserName: '',
				title:'',
				ordersNo:''
			};
		},
		onLoad(e) {
			this.ordersId = e.id
			this.byUserId = e.byUserId
			this.title = e.title
			this.ordersNo = e.ordersNo
			this.getUserInfo()
			let deviceInfo = {
				appid: plus.runtime.appid,
				imei: plus.device.imei, //设备标识
				p: plus.os.name === 'Android' ? 'a' : 'i', //平台类型，i表示iOS平台，a表示Android平台。
				md: plus.device.model, //设备型号
				app_version: plus.runtime.version,
				plus_version: plus.runtime.innerVersion, //基座版本号
				os: plus.os.version,
				net: '' + plus.networkinfo.getCurrentType()
			};
			this.sendDate = Object.assign(deviceInfo, this.sendDate);
		},
		methods: {
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById?userId="+this.byUserId).then(res => {
					if (res.code == 0) {
						this.byuserName = res.data.userName
					}
				});
			},
			// 详情图删除
			removeImg(index) {
				console.log(index)
				this.Imgo.splice(index, 1)
			},
			close(e) {
				this.imageList.splice(e, 1);
			},
			send() {
				//发送反馈
				this.sendDate.image = this.Imgo
				this.sendDate.image = this.sendDate.image.toString()

				// console.log(JSON.stringify(this.sendDate));
				// if (!this.sendDate.title) {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请输入投诉标题'
				// 	});
				// 	return;
				// }
				if (!this.sendDate.content) {
					uni.showToast({
						icon: 'none',
						title: '请输入投诉内容'
					});
					return;
				}
				if (!this.Imgo) {
					uni.showToast({
						icon: 'none',
						title: '请上传投诉图片'
					});
					return;
				}
				this.$queue.showLoading('加载中...');
				let userId = this.$queue.getData("userId");
				this.$Request.postJson('/app/message/insertMessage', {
					// ordersId: this.ordersId,
					byUserId: this.byUserId,
					title: this.title,
					image: this.sendDate.image,
					userId: userId,
					content: this.sendDate.content,
					state: 3,
					userName: this.byuserName,
					platform:3,
					platformId:this.ordersId,
					url:this.ordersNo
				}).then(res => {
					if (res.code === 0) {
						uni.showToast({
							title: '投诉成功'
						});
						setTimeout(function() {
							// uni.navigateBack();
							uni.redirectTo({
								url:'/my/order/tousuList'
							})
						}, 1000);
					} else {
						uni.hideLoading();
						uni.showModal({
							showCancel: false,
							title: '投诉失败',
							content: res.msg
						});
					}
				});
			}, // 图片上传
			addImages(e) {
				let that = this
				uni.chooseImage({
					count: 6,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: this.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								// url: 'https://xichewap.xianmxkj.com/sqx_fast/alioss/upload',
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									if (e == 2) {
										console.log(JSON.parse(uploadFileRes.data).data)
										that.Imgo.push(JSON.parse(uploadFileRes.data).data)
									}

									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			},
		}
	};
</script>

<style>
	@font-face {
		font-family: uniicons;
		font-weight: normal;
		font-style: normal;
		src: url('https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf') format('truetype');
	}

	page {
		background-color: #ffffff !important;
	}

	view {
		font-size: 28upx;
	}


	/*问题反馈*/
	.feedback-title {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 20upx;
		color: #8f8f94;
		font-size: 28upx;
	}

	.feedback-star-view.feedback-title {
		justify-content: flex-start;
		margin: 0;
	}

	.feedback-body {
		font-size: 32upx;
		padding: 16upx;
		margin: 16upx;
		border-radius: 16upx;
		background: #F5F5F5;
		/* color: #FFF; */
	}

	.feedback-textare {
		height: 200upx;
		font-size: 34upx;
		line-height: 50upx;
		width: 100%;
		box-sizing: border-box;
		padding: 20upx 30upx 0;
	}

	.feedback-input {
		font-size: 32upx;
		height: 60upx;
		/* padding: 15upx 20upx; */
		line-height: 60upx;
	}


	.feedback-submit {
		background: #0175FE;
		color: #ffffff;
		margin: 20upx;
		margin-top: 32upx;
	}
</style>
