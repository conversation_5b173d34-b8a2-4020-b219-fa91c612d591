<template>
	<u-index-list :scrollTop="scrollTop">
		<!-- <view class="carList">
			<scroll-view scroll-x="true" enable-flex="true" class="carLists">
				<view class="carList-item" v-for="(item,index) in carList" :key="index">
					<view class="carList-item-img">
						<image :src="item.img" mode=""></image>
					</view>
					<view class="carList-item-txt">
						{{item.title}}
					</view>
				</view>
			</scroll-view>
		</view> -->
		<view v-for="(item, index) in indexList" :key="index" >
			<u-index-anchor :index="item.letter" />
			<view class="list-cell" v-for="(cell, ind) in item.carList" :key="ind" @click="getCartType(cell)">
				<image style="width: 50rpx;height: 50rpx;border-radius: 50%;margin-right: 54rpx;" :src="cell.brand_logo"
					mode=""></image>
				{{cell.brand_name}}
			</view>
		</view>
	</u-index-list>
</template>

<script>
	export default {
		data() {
			return {
				carList: [],
				scrollTop: 0,
				indexList: []
			};
		},
		onLoad() {
			this.getheadlist()
		},
		onShow() {

		},
		methods: {
			getCartType(e){
				console.log(e)
				uni.setStorageSync("carClass",e)
				uni.navigateBack()
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			getheadlist() {
				this.$Request.get("/app/car/selectCarApi").then(res => {
					if (res.code == 0) {
						this.indexList = res.data
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.list-cell {
		display: flex;
		box-sizing: border-box;
		width: 100%;
		padding: 10px 24rpx;
		overflow: hidden;
		color: #323233;
		font-size: 14px;
		line-height: 24px;
		background-color: #fff;
	}

	.carList {
		width: 100%;
		height: 150rpx;
		background-color: #ffffff;

		.carLists {
			width: 100%;
			height: 150rpx;
			white-space: nowrap;
			/*  不换行 */
			padding-left: 30rpx;
		}

		.carList-item {
			width: 100rpx;
			height: 100%;
			display: inline-block;
			/* 行内元素 */
			padding-top: 40rpx;
			margin-right: 30rpx;

			.carList-item-img {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 50rpx;
					height: 50rpx;
					border-radius: 50%;
				}
			}

			.carList-item-txt {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #323233;
				font-size: 26rpx;
			}
		}
	}
</style>
