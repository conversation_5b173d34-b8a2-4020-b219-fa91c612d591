<template>
	<view>
		<view class="box">
			<view class="" style="padding: 50upx 50upx;">
				<view>您可享受 [ {{province}}{{city?city:''}}{{district?district:''}} ] 当前区域{{feiRate}}的佣金奖励</view>
				<view class="flex align-center justify-between margin-top">
					<view  >
						<view class="margin-bottom-sm">佣金奖励</view>
						<view style="font-size: 58upx;">{{yearMoney?yearMoney:'0'}}</view>
					</view>
					<view class="btn" @click="goTx()">提现</view>
				</view>
			</view>
			<view class="boxbtn">
				<view class="flex-sub ">
					<view class="text-sm margin-bottom-xs">本年收益</view>
					<view style="font-size: 40upx;">{{yearMoney?yearMoney:'0'}}</view>
				</view>
				<view class="flex-sub ">
					<view class="text-sm margin-bottom-xs">本月收益</view>
					<view style="font-size: 40upx;">{{monthMoney?monthMoney:'0'}}</view>
				</view>
				<view class="flex-sub ">
					<view class="text-sm margin-bottom-xs">今日收益</view>
					<view style="font-size: 40upx;">{{dayMoney?dayMoney:'0'}}</view>
				</view>
			</view>
		</view>

		<view class="bg margin padding" style="border-radius: 24upx;">
			<view class="flex align-center margin-bottom">
				<view style="background: #0274FB;width: 6upx;height: 32upx;"></view>
				<view class="text-30 margin-left-xs">收益明细</view>
			</view>
			<!-- <view class="margin-tb" style="width: 100%;height: 1rpx;background: #E6E6E6;"></view> -->
			<view v-for="(item,index) in list" :key="index">
				<view class="flex align-center justify-between">
					<view>
						<view class="flex align-center">
							<view class="  ">{{item.userName}}</view>
							<!-- <image src="../static/copy.png" style="width: 30rpx;height: 30rpx;margin-left: 5upx;"
								@click.stop="copyClick(item.title)"></image> -->
						</view>
						<view class="margin-tb-sm">{{item.content}}</view>
						<view style="color: #999999;">{{item.createTime}}</view>
					</view>
					<view class="flex align-center" style="font-size: 38upx;font-weight: bold;">
						<view v-if="item.type==1">+</view>
						<view v-if="item.type==2">-</view>
						<view>{{item.money}}</view>
					</view>
				</view>
				<view v-if="list.length-1!=index" class="margin-tb" style="width: 100%;height: 1rpx;background: #E6E6E6;"></view>
			</view>
			<view class="" style="width: 100%;" v-if="list.length==0">
				<image src="../../static/images/empty.png" mode="widthFix"></image>
				<view style="width: 100%;text-align: center;">暂无数据</view>
			</view>
		</view>
	</view>
</template>

<script>
	import empty from "../../components/empty.vue"
	export default {
		components:{
			empty:empty
		},
		data() {
			return {
				dayMoney: '',
				monthMoney: '',
				yearMoney: '',
				page: 1,
				limit: 10,
				list: [],
				feiRate:'',
				province:'',
				city:'',
				district:''
			};
		},
		onLoad() {
			this.getUserInfo()
			this.getDetail()
			this.getAgentList()
			
		},
		onShow() {

		},
		methods: {
			copyClick(copy) {
				uni.setClipboardData({
					data: copy,
					success: function(res) {
						uni.getClipboardData({
							success: function(res) {
								uni.showToast({
									title: "复制成功",
									icon: 'none',
								});
							},
						});
					},
				});
			},
			goTx() {
				uni.navigateTo({
					url: '/my/wallet/wallet'
				})
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						
						this.province = res.data.province
						this.city = res.data.city
						this.district = res.data.district
						this.feiRate = parseFloat(res.data.feiRate *100).toFixed(0)+'%'//代理商佣金比例
					}
				});
			},
			getDetail() {
				let data = {
					type: 2
				}
				this.$Request.get("/app/orders/selectTeamStatisticsByType", data).then(res => {
					if (res.code == 0) {
						this.dayMoney = res.data.dayMoney
						this.monthMoney = res.data.monthMoney
						this.yearMoney = res.data.yearMoney
					}
				})
			},
			getAgentList() {
				let data = {
					page: this.page,
					limit: this.limit,
					// classify: 20
				}
				this.$Request.get("/app/userMoney/getAgentProfitList", data).then(res => {
					if (res.code == 0) {
						if (this.page == 1) this.list = []
						this.list = [...this.list, ...res.data.records]
					}
				})
			}
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			this.getAgentList()
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getAgentList()
		},
	}
</script>


<style lang="less">
	page {
		background: #F2F2F2;
	}

	.bg {
		background: #FFFFFF;
	}

	.box {
		margin: 30upx;

		background: linear-gradient(90deg, #6C91FE 0%, #4965F9 100%);
		border-radius: 24upx;
		color: #FFFFFF;
	}

	.btn {
		background: linear-gradient(90deg, #9DB6FF 0%, #7D92FF 100%);
		border-radius: 35upx;
		padding: 16upx 45upx;
		color: #FFFFFF;
	}

	.boxbtn {
		padding: 30upx 50upx;
		background: linear-gradient(-90deg, #6B90FE 0%, #4B68F9 100%);
		border-radius: 0px 0px 24upx 24upx;
		display: flex;
		align-items: center;
		justify-content: space-between;

	}
</style>
