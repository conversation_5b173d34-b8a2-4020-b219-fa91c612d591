<template>
	<view>
		<view class="padding bg text-white radius u-skeleton-fillet">
			<view class="margin-tb-sm" v-for="(item, index) in commentList" :key='index'>
				<view class="flex justify-between">
					<!-- <u-avatar :src="item.avatar" size="48"></u-avatar> -->
					<image :src="item.avatar?item.avatar:'../../static/logo.png'" style="width: 50upx;height: 50upx;border-radius: 50upx;"></image>
					<view class="flex-sub margin-left-sm" style="line-height: 46upx;">{{item.userName}}</view>
					<view class="flex">
						<u-icon v-for="ite in item.score" :key='ite' color="#2087fe" name="star-fill"></u-icon>
					</view>
				</view>
				<view class="margin-top-sm">{{item.content}}</view>
				<view v-if="commentList.length-1!=index" style="width: 100%;height: 1rpx;background: #f5f5f5;margin-top: 20upx;"></view>
			</view>
		</view>
		<empty v-if="commentList.length == 0"></empty>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				commentList: [],
				id: '',
				page: '1',
				limit: '10',
				
			}
		},
		onLoad(option) {
			if (option.id) {
				this.id = option.id
				this.getOrderComment()
			}

		},
		onShow() {

		},
		methods: {
			// 评论
			getOrderComment() {
				this.$Request.get("/app/takingComment/selectOrderTakingComment", {
					id: this.id,
					page: this.page,
					limit: this.limit
				}).then(res => {
					if (res.code == 0) {
						if(this.page==1)this.commentList=[]
						this.commentList = [...this.commentList, ...res.data.list]
						uni.stopPullDownRefresh();
					}
				});
			},
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			this.getOrderComment()
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getOrderComment()
		},
	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}

	.bg {
		background-color: #FFFFFF;
	}
</style>
