<template>
	<view>
		<view class="flex align-center justify-between  bg padding">
			<view v-for="(item,index) in list" :key="index" :class="tabIndex==index?'active':''" @click="change(index)">
				{{item.name}}
			</view>
		</view>
		<view class="listbox" v-for="(item,index) in yhqList" :key="index">
			<view class="flex align-start justify-between">
				<view>
					<view style="color: #000000;font-size: 30upx;">{{item.couponName}}</view>
					<view style="color: #999999;font-size: 24upx;margin-top: 10upx;" v-if="item.expirationTime">有效期至{{item.expirationTime}}</view>
					<view style="color: #999999;font-size: 24upx;margin-top: 10upx;" v-else>永久有效</view>
				</view>
				<view style="color: #FD3C44;font-size: 30upx;">¥<text
						style="font-size: 48upx;font-weight: bold;">{{item.money}}</text></view>
			</view>
			<view style="width: 100%;border-top:1rpx dashed #E6E6E6;margin: 30upx 0upx;"></view>
			<view class="flex align-center justify-between">
				<view v-if="item.minMoney">满{{item.minMoney}}元可用</view>
				<view v-else>无门槛使用</view>
				<view class="btn" @click="shiyong" v-if="item.status==0">立即使用</view>
			</view>
		</view>
		<empty v-if="yhqList.length == 0"></empty>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty,
		},
		data() {
			return {
				list: [{
					id: 1,
					name: '可使用'
				}, {
					id: 2,
					name: '已使用'
				}, {
					id: 3,
					name: '已失效'
				}],
				tabIndex: '0',
				page: 1,
				limit: 10,
				yhqList: [],
				count: 0
			}
		},
		onLoad() {
			this.getList()
		},
		onShow() {

		},
		methods: {
			shiyong() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			change(index) {
				this.tabIndex = index
				this.page = 1
				this.getList()
			},
			getList() {
				let data = {
					page: this.page,
					limit: this.limit,
					status: this.tabIndex
				}
				this.$Request.getT('/app/couponUser/getMyCouponList', data).then(res => {
					if (res.code == 0) {
						let data = res.data.records
						for (var i = 0; i < data.length; i++) {
							if(data[i].expirationTime){
								data[i].expirationTime = data[i].expirationTime.substring(0, 10)
							}
						}
						if (this.page == 1) {
							this.yhqList = res.data.records;
						} else {
							this.yhqList = [...this.yhqList, ...res.data.records]
						}
						this.count = res.data.total

					}
				});
			}
		},
		onReachBottom: function() {
			if (this.yhqList.length == this.count) {
				uni.showToast({
					title: '已经到底了~',
					icon: 'none'
				})
			} else {
				this.page = this.page + 1;
				this.getList()
			}

		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getList()
		},
	}
</script>

<style>
	page {
		background: #F5F5F5;
	}

	.bg {
		background: #ffffff;
	}

	.active {
		color: #025EFD;
		font-size: 32upx;
		font-weight: bold;
	}

	.listbox {
		margin: 30upx;
		background: #FFFFFF;
		border-radius: 8upx;
		padding: 30upx;
	}

	.btn {
		color: #FD3C44;
		border: 1rpx solid #FD3C44;
		border-radius: 55upx;
		padding: 8upx 25upx;
	}
</style>
