{
    "name" : "码兄陪诊",
    "appid" : "__UNI__7A1D7EC",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
	"sassImplementationName": "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "OAuth" : {},
            "Maps" : {},
            "Geolocation" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "autoSdkPermissions" : true
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "android" ],
                        "appid" : "wx9d2aafedc37441a7",
                        "UniversalLinks" : ""
                    },
                    "alipay" : {
                        "__platform__" : [ "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx9d2aafedc37441a7",
                        "UniversalLinks" : ""
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx9d2aafedc37441a7",
                        "appsecret" : "d73468b34d33de7e31b0e508e7acf130",
                        "UniversalLinks" : ""
                    }
                },
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "78a7bfb6245bc33b12404249a2e92857",
                        "appkey_android" : "78a7bfb6245bc33b12404249a2e92857"
                    }
                },
                "push" : {
                    "unipush" : {}
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "78a7bfb6245bc33b12404249a2e92857",
                        "appkey_android" : "78a7bfb6245bc33b12404249a2e92857"
                    },
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : "",
                    "xxxhdpi" : ""
                },
                "ios" : {
                    "appstore" : "",
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "notification" : "",
                        "notification@2x" : "",
                        "proapp@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : ""
                    },
                    "iphone" : {
                        "app@2x" : "",
                        "app@3x" : "",
                        "notification@2x" : "",
                        "notification@3x" : "",
                        "settings@2x" : "",
                        "settings@3x" : "",
                        "spotlight@2x" : "",
                        "spotlight@3x" : ""
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx9d2aafedc37441a7",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : false,
            "es6" : false
        },
        "__usePrivacyCheck__" : false,
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序定位"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation" ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "router" : {
            "mode" : "history"
        },
        "devServer" : {
            "https" : false,
            "port" : 8080,
            "disableHostCheck" : true,
            "proxy" : {
                // 可代理多个
                "/TencentGet" : {
                    "target" : "https://apis.map.qq.com/ws/geocoder/v1/", // 腾讯地图逆地址解析
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/TencentGet" : ""
                    }
                }
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "WYJBZ-BOVK2-QZSU2-CJHY2-7BFHE-JEFNP"
                }
            }
        }
    }
}
