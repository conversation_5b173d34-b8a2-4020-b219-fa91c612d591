<template>
	<view class="content">
		<view class="navbar">
			<view v-for="(item, index) in tabList" :key="index" class="flex-sub"
				 @click="tabClicks(item.state)">
				<view class="nav-item" :class="{ current: tabFromIndex === item.state }">{{ item.text }}</view>
				<view class="text-center" style="margin-top: -30upx;">
					<image v-if="tabFromIndex === item.state " src="../../static/images/index/wx.png" style="width: 55upx;height: 16upx;margin-top: -5upx;"></image>
				</view>
			</view>
		</view>
		<!-- #ifdef H5 -->
		<view style="margin-top: 80upx;">
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<view style="margin-top: 90upx;">
		<!-- #endif -->
			<view v-for="(item, index) in list" :key="index" class="item" @click="goDet(item.content)">
				<view class="flex justify-between"
					style="font-size: 30upx;width: 100%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap">
					<view>{{ item.title }}</view>
					<view v-if="item.isSee == 0"
						style="height: 32rpx;width: 32rpx;border-radius: 100rpx;background-color: red;color: #FFF;text-align: center;">
					</view>
				</view>
				<view style="color: #999999;font-size: 28upx;margin-top: 10upx;">{{ item.content }}</view>
			
				<view style="margin-top: 10upx;color: #999999;font-size: 28upx;text-align: right;">{{ item.createAt }}
				</view>
				<view v-if="list.length-1!=index" style="width: 100%;height: 1rpx;background: #f5f5f5;margin-top: 30upx;"></view>
				
			</view>
		</view>
		<!-- <view v-if="list.length === 0" style="background: #1c1b20;text-align: center;padding-top: 140upx;color: #FFFFFF;">暂无消息</view> -->
		<empty v-if="list.length === 0" content="暂无消息" show="false"></empty>
	</view>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	import empty from '@/components/empty';

	export default {
		components: {
			uniLoadMore,
			empty
		},
		data() {
			return {
				tabFromIndex: 5,
				tabCurrentIndex: 0,
				fromInfo: 5,
				list: [],
				page: 1,
				limit: 10,
				scrollTop: false,
				tabList: [{
						state: 5,
						text: '用户消息',
						totalElements: 0
					},
					{
						state: 4,
						text: '订单消息',
						totalElements: 0
					}
				]
			};
		},
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop > 200;
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			this.loadData();
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.loadData();
		},
		onLoad(options) {
			this.$queue.showLoading("加载中...")
			this.loadData();
		},
		methods: {
			goDet(e) {
				console.log(e.indexOf('下单'))
				if (e.indexOf('下单') != -1) {
					uni.switchTab({
						url:'/pages/order/index'
					})
				}else if (e.indexOf('任务') != -1) {
					uni.switchTab({
						url:'/pages/order/index'
					})
				} 
			},
			//顶部渠道点击
			tabClicks(index) {
				this.list = [];
				this.page = 1;
				this.tabFromIndex = index;
				this.$queue.showLoading("加载中...")
				this.loadData();
			},
			//获取消息列表
			loadData() {
				let that = this;
				let number = 10;
				let token = this.$queue.getData('token');
				if (token) {
					let data = {
						page: this.page,
						limit: this.limit,
						state: this.tabFromIndex
					}
					this.$Request.getT('/app/message/selectMessageByUserId', data).then(res => {
						if (res.code === 0) {
							if (this.page == 1) {
								this.list = res.data.list
							} else {
								res.data.list.forEach(d => {
									this.list.push(d);
								});
							}
						}
						uni.hideLoading();
						uni.stopPullDownRefresh();
					});
				}
			}
		}
	};
</script>

<style lang="scss">
	page,
	page {
		background: #ffffff;
	}

	.content {
		background: #ffffff;
		height: 100%;
	}

	.swiper-box {
		height: calc(100% - 40px);
	}

	.list-scroll-content {
		height: 100%;
	}

	.navbar {
		display: flex;
		height: 40px;
		padding: 0 5px;
		// background: #1E1F31;
		// box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
		color: #000000;
		position: relative;
		background: #FFFFFF;
		position: fixed;
		/* #ifdef H5 */
		top:80upx;
		/* #endif */
		/* #ifndef H5 */
		top: 0;
		/* #endif */
		left: 0;
		right: 0;
		z-index: 10;

		.nav-item {
			
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 15px;
			// color: #FFFFFF;
			position: relative;

			&.current {
				color: #557EFD;

				// &:after {
				// 	content: '';
				// 	position: absolute;
				// 	left: 50%;
				// 	bottom: 0;
				// 	transform: translateX(-50%);
				// 	width: 44px;
				// 	height: 0;
				// 	border-bottom: 2px solid #557EFD;
				// }
			}
		}
	}

	.uni-swiper-item {
		height: auto;
	}

	page {
		background-color: #F7F7F7;
	}

	.item {
		background: #FFFFFF;
		padding: 16rpx;
		margin: 16rpx;
		font-size: 28rpx;
		// box-shadow: 7px 9px 34px rgba(0, 0, 0, 0.1);
		border-radius: 16upx;
	}


	/* load-more */
	.uni-load-more {
		display: flex;
		flex-direction: row;
		height: 40px;
		align-items: center;
		justify-content: center;
	}

	.uni-load-more__text {
		font-size: 14px;
		// color: #999;
	}

	.uni-load-more__img {
		height: 24px;
		width: 24px;
		margin-right: 10px;
	}

	.uni-load-more__img>view {
		position: absolute;
	}

	.uni-load-more__img>view view {
		width: 6px;
		height: 2px;
		border-top-left-radius: 1px;
		border-bottom-left-radius: 1px;
		background: #999;
		position: absolute;
		opacity: 0.2;
		transform-origin: 50%;
		animation: load 1.56s ease infinite;
	}

	.uni-load-more__img>view view:nth-child(1) {
		transform: rotate(90deg);
		top: 2px;
		left: 9px;
	}

	.uni-load-more__img>view view:nth-child(2) {
		transform: rotate(180deg);
		top: 11px;
		right: 0;
	}

	.uni-load-more__img>view view:nth-child(3) {
		transform: rotate(270deg);
		bottom: 2px;
		left: 9px;
	}

	.uni-load-more__img>view view:nth-child(4) {
		top: 11px;
		left: 0;
	}

	.load1,
	.load2,
	.load3 {
		height: 24px;
		width: 24px;
	}

	.load2 {
		transform: rotate(30deg);
	}

	.load3 {
		transform: rotate(60deg);
	}

	.load1 view:nth-child(1) {
		animation-delay: 0s;
	}

	.load2 view:nth-child(1) {
		animation-delay: 0.13s;
	}

	.load3 view:nth-child(1) {
		animation-delay: 0.26s;
	}

	.load1 view:nth-child(2) {
		animation-delay: 0.39s;
	}

	.load2 view:nth-child(2) {
		animation-delay: 0.52s;
	}

	.load3 view:nth-child(2) {
		animation-delay: 0.65s;
	}

	.load1 view:nth-child(3) {
		animation-delay: 0.78s;
	}

	.load2 view:nth-child(3) {
		animation-delay: 0.91s;
	}

	.load3 view:nth-child(3) {
		animation-delay: 1.04s;
	}

	.load1 view:nth-child(4) {
		animation-delay: 1.17s;
	}

	.load2 view:nth-child(4) {
		animation-delay: 1.3s;
	}

	.load3 view:nth-child(4) {
		animation-delay: 1.43s;
	}

	@-webkit-keyframes load {
		0% {
			opacity: 1;
		}

		100% {
			opacity: 0.2;
		}
	}
</style>
