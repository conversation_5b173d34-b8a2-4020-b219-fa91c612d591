<template>
	<view>
		<image class="bgimg" :src="imgBg" mode="widthFix"></image>

		<view class="info">
			<view class="info-box">
				<view class="info-box-title">
					区县代理申请
				</view>
				<view class="" v-if="applyId" style="text-align: center;color: red;">
					信息已提交，请等待后台审核或联系客服
				</view>
				<view class="info-box-item">
					<view class="info-box-item-name">联系人姓名</view>
					<u-input placeholder="请输入联系人姓名" v-model="applyName" :clearable="false"></u-input>
				</view>
				<view class="line"></view>
				<view class="info-box-item">
					<view class="info-box-item-name">联系人手机号</view>
					<u-input placeholder="请输入联系人手机号" v-model="applyPhone" :clearable="false"></u-input>
				</view>
				<view class="line"></view>
				<view class="info-box-item" @click="getcity()">
					<view class="info-box-item-name">地址</view>
					<u-input placeholder="请选择所在区域地址" v-model=" applyContent" @click="getcity()" :disabled="true"
						:clearable="false">
					</u-input>
				</view>
			</view>
		</view>
		<view class="sub">

			<view class="sub-box" v-if="!applyId">
				<view class="sub-box-l" @click="submit()">确认提交</view>
				<view class="sub-box-r" @click="Changekefu()">直接联系 </view>
			</view>
			<view class="sub-box" v-else>
				<view class="sub-box-r" style="width: 100%;" @click="Changekefu()">直接联系 </view>
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				applyName: '',
				applyPhone: '',
				applyContent: '',
				applyId: '',
				imgBg: '',
			};
		},
		onLoad() {
			this.getDetail()
			this.getBgImg()
		},
		onShow() {

		},
		methods: {
			//获取背景图
			getBgImg() {
				this.$Request.get('/app/common/type/334').then(res => {
					if (res.code == 0) {
						this.imgBg = res.data.value
					}
				});
			},
			getDetail() {
				let data = {
					classify: 2
				}
				this.$Request.get("/app/apply/selectApplyByUserIdAndClassify", data).then(res => {
					if (res.code == 0 && res.data) {
						this.applyName = res.data.applyName
						this.applyPhone = res.data.applyPhone
						this.applyContent = res.data.applyContent
						this.applyId = res.data.status

					}
				})
			},
			getcity() {
				// console.log(55555)
				let that = this
				uni.chooseLocation({
					success: function(res) {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						that.applyContent = res.address + res.name
					}
				});
			},
			Changekefu() {
				let SelKeFu = this.$queue.getData('SelKeFu');
				if (SelKeFu + '' == '1') { //手机号
					uni.makePhoneCall({
						phoneNumber: uni.getStorageSync('kefuPhone')
					});
				} else if (SelKeFu + '' == '2') { //企业微信
					let that = this
					// #ifdef MP-WEIXIN
					wx.openCustomerServiceChat({
						extInfo: {
							url: that.$queue.getData('kefu')
						},
						corpId: that.$queue.getData('kefuAppId'),
						success(res) {}
					})
					// #endif
					// #ifdef H5
					window.location.href = that.$queue.getData('kefu');
					// #endif
					// #ifdef APP
					let kefu = that.$queue.getData('kefu')
					console.log(kefu)
					plus.runtime.openURL(kefu, function(res) {});
					// #endif
				} else { //客服二维码页面
					uni.navigateTo({
						url: '/pages/kefu/kefu'
					})
				}
			},
			submit() {
				if (!this.applyName) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					})
					return
				}
				if (!this.applyPhone) {
					uni.showToast({
						title: '请输入联系人手机号码',
						icon: 'none'
					})
					return
				}
				if (!this.applyContent) {
					uni.showToast({
						title: '请输入地址',
						icon: 'none'
					})
					return
				}
				let data = {
					applyName: this.applyName,
					applyPhone: this.applyPhone,
					applyContent: this.applyContent,
					classify: 2
				}

				this.$Request.postJson("/app/apply/insertApply", data).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '提交成功！',
							icon: 'none'
						})
						setTimeout(function() {
							uni.navigateBack()
						}, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>

<style lang="less">
	page {
		background-color: #f5f5f5;
	}

	.bgimg {
		position: relative;
		width: 100%;
	}

	.info {
		// width: 90%;
		height: auto;
		display: flex;
		justify-content: center;
		position: absolute;
		top: 750rpx;
		background-image: url('../static/zsbg.png');
		background-size: 100% 100%;
		margin: 30upx;
		padding: 30upx;

		.info-box {
			width: 620rpx;

			.info-box-title {
				color: #333333;
				font-size: 32rpx;
				font-weight: 800;
				text-align: center;
			}

			.info-box-item {
				margin-top: 30rpx;

				.info-box-item-name {
					font-size: 28rpx;
					color: #333333;
					font-weight: 800;
					margin-bottom: 10rpx;
				}
			}

			.line {
				width: 100%;
				border: 1rpx solid #B4BCFF;
				margin-top: 20rpx;
			}
		}
	}

	.sub {
		width: 100%;
		height: 88rpx;
		display: flex;
		justify-content: center;
		position: absolute;
		top: 1430rpx;

		.sub-box {
			width: 613rpx;
			height: 100%;
			display: flex;
			justify-content: space-between;

			.sub-box-l {
				width: 290rpx;
				height: 88rpx;
				background: #97A8F8;
				border-radius: 44rpx;
				color: #ffffff;
				font-size: 28rpx;
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.sub-box-r {
				width: 290rpx;
				height: 88rpx;
				background: #435DF0;
				border-radius: 44rpx;
				color: #ffffff;
				font-size: 28rpx;
				font-weight: bold;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
</style>